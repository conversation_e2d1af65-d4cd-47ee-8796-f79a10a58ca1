"""
Production Configuration for Intelligent Reporting System
Secure configuration management with environment variables
"""

import os
from typing import List, Dict, Any
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ModelConfig:
    """AI Model configurations"""
    # Speech-to-Text
    WHISPER_MODEL: str = "base"  # Options: tiny, base, small, medium, large
    
    # Schema Retrieval
    SENTENCE_TRANSFORMER_MODEL: str = "all-MiniLM-L6-v2"
    
    # Text-to-SQL (Production Arctic Model)
    ARCTIC_TEXT2SQL_MODEL: str = "Snowflake/Arctic-Text2SQL-R1-7B"
    ARCTIC_MAX_LENGTH: int = 2048
    ARCTIC_TEMPERATURE: float = 0.1
    ARCTIC_TOP_P: float = 0.9
    
    # LoRA Fine-tuning configurations
    LORA_R: int = 16
    LORA_ALPHA: int = 32
    LORA_DROPOUT: float = 0.1
    LORA_TARGET_MODULES: List[str] = None
    
    # Sentiment Analysis
    SENTIMENT_MODEL: str = "distilbert-base-uncased-finetuned-sst-2-english"
    
    # Summarization
    SUMMARY_MODEL: str = "t5-small"
    SUMMARY_MAX_LENGTH: int = 150
    SUMMARY_MIN_LENGTH: int = 30
    
    def __post_init__(self):
        if self.LORA_TARGET_MODULES is None:
            self.LORA_TARGET_MODULES = ["q_proj", "v_proj", "k_proj", "o_proj"]

@dataclass
class DatabaseConfig:
    """Snowflake database configurations"""
    # Snowflake Connection (from environment variables)
    ACCOUNT: str = os.getenv("SNOWFLAKE_ACCOUNT", "")
    USER: str = os.getenv("SNOWFLAKE_USER", "")
    PASSWORD: str = os.getenv("SNOWFLAKE_PASSWORD", "")
    WAREHOUSE: str = os.getenv("SNOWFLAKE_WAREHOUSE", "COMPUTE_WH")
    DATABASE: str = os.getenv("SNOWFLAKE_DATABASE", "")
    SCHEMA: str = os.getenv("SNOWFLAKE_SCHEMA", "PUBLIC")
    ROLE: str = os.getenv("SNOWFLAKE_ROLE", "")
    
    # Connection settings
    CONNECTION_TIMEOUT: int = 30
    QUERY_TIMEOUT: int = 300
    MAX_RETRIES: int = 3
    
    def validate(self) -> bool:
        """Validate required Snowflake credentials"""
        required_fields = [self.ACCOUNT, self.USER, self.PASSWORD, self.DATABASE]
        return all(field.strip() for field in required_fields)

@dataclass
class SecurityConfig:
    """Security and compliance configurations"""
    # SQL Security
    ALLOWED_SCHEMAS: List[str] = None
    ALLOWED_TABLES: List[str] = None
    BLOCKED_KEYWORDS: List[str] = None
    
    # Input validation
    MAX_PROMPT_LENGTH: int = 2000
    MAX_AUDIO_SIZE_MB: int = 25
    MAX_RESULTS_ROWS: int = 500
    
    # API Security
    API_KEY: str = os.getenv("API_KEY", "")
    RATE_LIMIT_PER_MINUTE: int = 60
    ENABLE_CORS: bool = True
    ALLOWED_ORIGINS: List[str] = None
    
    # Audit logging
    ENABLE_AUDIT_LOGGING: bool = True
    AUDIT_LOG_PATH: str = "/tmp/audit_log.json"
    LOG_RETENTION_DAYS: int = 30
    
    def __post_init__(self):
        if self.ALLOWED_SCHEMAS is None:
            self.ALLOWED_SCHEMAS = ["PUBLIC", "ANALYTICS", "REPORTING"]
        
        if self.BLOCKED_KEYWORDS is None:
            self.BLOCKED_KEYWORDS = [
                "DROP", "DELETE", "UPDATE", "INSERT", "CREATE", "ALTER", 
                "TRUNCATE", "GRANT", "REVOKE", "EXEC", "EXECUTE"
            ]
        
        if self.ALLOWED_ORIGINS is None:
            self.ALLOWED_ORIGINS = ["http://localhost:3000", "http://localhost:5173"]

@dataclass
class ServerConfig:
    """Server and deployment configurations"""
    # FastAPI settings
    HOST: str = "0.0.0.0"
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # ngrok settings (for development)
    NGROK_AUTH_TOKEN: str = os.getenv("NGROK_AUTH_TOKEN", "")
    ENABLE_NGROK: bool = os.getenv("ENABLE_NGROK", "true").lower() == "true"
    
    # Production settings
    WORKERS: int = int(os.getenv("WORKERS", "1"))
    MAX_CONCURRENT_REQUESTS: int = 100
    REQUEST_TIMEOUT: int = 300
    
    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090

@dataclass
class LoRAConfig:
    """LoRA fine-tuning specific configurations"""
    # Training parameters
    LEARNING_RATE: float = 2e-4
    BATCH_SIZE: int = 4
    GRADIENT_ACCUMULATION_STEPS: int = 4
    NUM_EPOCHS: int = 3
    WARMUP_STEPS: int = 100
    
    # LoRA parameters
    R: int = 16
    ALPHA: int = 32
    DROPOUT: float = 0.1
    TARGET_MODULES: List[str] = None
    
    # Training data
    TRAINING_DATA_PATH: str = os.getenv("TRAINING_DATA_PATH", "./training_data.json")
    VALIDATION_SPLIT: float = 0.1
    
    # Model saving
    OUTPUT_DIR: str = "./lora_model"
    SAVE_STEPS: int = 500
    EVAL_STEPS: int = 500
    
    def __post_init__(self):
        if self.TARGET_MODULES is None:
            self.TARGET_MODULES = ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]

class ProductionConfig:
    """Main configuration class that combines all configs"""
    
    def __init__(self):
        self.models = ModelConfig()
        self.database = DatabaseConfig()
        self.security = SecurityConfig()
        self.server = ServerConfig()
        self.lora = LoRAConfig()
        
        # Environment detection
        self.environment = os.getenv("ENVIRONMENT", "development")
        self.is_production = self.environment == "production"
        self.is_development = self.environment == "development"
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration settings"""
        if self.is_production:
            # Production validations
            if not self.database.validate():
                raise ValueError("Missing required Snowflake credentials for production")
            
            if not self.security.API_KEY:
                raise ValueError("API_KEY is required for production")
            
            if self.server.DEBUG:
                raise ValueError("DEBUG mode should be disabled in production")
        
        # General validations
        if self.models.ARCTIC_MAX_LENGTH > 4096:
            raise ValueError("ARCTIC_MAX_LENGTH should not exceed 4096 tokens")
        
        if self.security.MAX_RESULTS_ROWS > 10000:
            raise ValueError("MAX_RESULTS_ROWS should not exceed 10000 for performance")
    
    def get_snowflake_connection_params(self) -> Dict[str, Any]:
        """Get Snowflake connection parameters"""
        return {
            "account": self.database.ACCOUNT,
            "user": self.database.USER,
            "password": self.database.PASSWORD,
            "warehouse": self.database.WAREHOUSE,
            "database": self.database.DATABASE,
            "schema": self.database.SCHEMA,
            "role": self.database.ROLE,
            "client_session_keep_alive": True,
            "network_timeout": self.database.CONNECTION_TIMEOUT,
        }
    
    def get_model_cache_dir(self) -> Path:
        """Get model cache directory"""
        cache_dir = Path(os.getenv("MODEL_CACHE_DIR", "./model_cache"))
        cache_dir.mkdir(exist_ok=True)
        return cache_dir
    
    def get_lora_adapter_path(self) -> Path:
        """Get LoRA adapter path"""
        return Path(self.lora.OUTPUT_DIR)
    
    def print_config_summary(self):
        """Print configuration summary (without sensitive data)"""
        print("🔧 Configuration Summary:")
        print(f"  Environment: {self.environment}")
        print(f"  Arctic Model: {self.models.ARCTIC_TEXT2SQL_MODEL}")
        print(f"  Snowflake Database: {self.database.DATABASE}")
        print(f"  Server Port: {self.server.PORT}")
        print(f"  Max Results: {self.security.MAX_RESULTS_ROWS}")
        print(f"  LoRA R: {self.lora.R}, Alpha: {self.lora.ALPHA}")
        print(f"  Audit Logging: {self.security.ENABLE_AUDIT_LOGGING}")

# Global configuration instance
config = ProductionConfig()

# Environment variable template for .env file
ENV_TEMPLATE = """
# Snowflake Configuration
SNOWFLAKE_ACCOUNT=your_account.region
SNOWFLAKE_USER=your_username
SNOWFLAKE_PASSWORD=your_password
SNOWFLAKE_WAREHOUSE=COMPUTE_WH
SNOWFLAKE_DATABASE=your_database
SNOWFLAKE_SCHEMA=your_schema
SNOWFLAKE_ROLE=your_role

# Security
API_KEY=your_secure_api_key_here

# Server Configuration
PORT=8000
DEBUG=false
ENVIRONMENT=production
WORKERS=4

# ngrok (for development)
NGROK_AUTH_TOKEN=your_ngrok_token
ENABLE_NGROK=false

# Training Data
TRAINING_DATA_PATH=./your_training_data.json
MODEL_CACHE_DIR=./model_cache
"""

def create_env_template():
    """Create .env template file"""
    with open(".env.template", "w") as f:
        f.write(ENV_TEMPLATE.strip())
    print("✅ Created .env.template file")
    print("📝 Copy to .env and fill in your credentials")
