#!/usr/bin/env python3
"""
Production Server for Intelligent Reporting System
Secure, scalable FastAPI server with proper configuration management
"""

import os
import sys
import logging
from pathlib import Path
import uvicorn
from dotenv import load_dotenv

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Load environment variables from .env file
load_dotenv()

# Import configuration and application
try:
    from config import ProductionConfig
    config = ProductionConfig()
    
    # Print configuration summary (without sensitive data)
    config.print_config_summary()
    
except ImportError as e:
    print(f"❌ Failed to import configuration: {e}")
    print("💡 Make sure config.py is in the same directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Configuration error: {e}")
    print("💡 Check your .env file and configuration settings")
    sys.exit(1)

def setup_logging():
    """Setup production logging configuration"""
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    
    # Create logs directory if it doesn't exist
    log_dir = Path("./logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific log levels for noisy libraries
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("torch").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured at {log_level} level")
    return logger

def validate_environment():
    """Validate production environment and dependencies"""
    logger = logging.getLogger(__name__)
    
    # Check required environment variables
    required_vars = [
        "SNOWFLAKE_ACCOUNT",
        "SNOWFLAKE_USER", 
        "SNOWFLAKE_PASSWORD",
        "SNOWFLAKE_DATABASE"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        return False
    
    # Check if running in production mode
    if config.is_production:
        if not config.security.API_KEY:
            logger.error("API_KEY is required for production")
            return False
        
        if config.server.DEBUG:
            logger.error("DEBUG mode should be disabled in production")
            return False
    
    # Check model cache directory
    model_cache_dir = config.get_model_cache_dir()
    if not model_cache_dir.exists():
        logger.info(f"Creating model cache directory: {model_cache_dir}")
        model_cache_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ Environment validation passed")
    return True

def create_app():
    """Create and configure FastAPI application"""
    logger = logging.getLogger(__name__)
    
    try:
        # Import the FastAPI app from the notebook code
        # In production, you would extract this into a separate module
        logger.info("🚀 Creating FastAPI application...")
        
        # For now, we'll create a basic app structure
        # In the full implementation, you would import from your main app module
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        
        app = FastAPI(
            title="Intelligent Reporting API",
            description="Production AI-powered voice-to-insights reporting platform",
            version="1.0.0",
            debug=config.server.DEBUG
        )
        
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.security.ALLOWED_ORIGINS,
            allow_credentials=True,
            allow_methods=["GET", "POST"],
            allow_headers=["*"],
        )
        
        # Add basic health check
        @app.get("/health")
        async def health_check():
            return {
                "status": "healthy",
                "environment": config.environment,
                "version": "1.0.0"
            }
        
        logger.info("✅ FastAPI application created successfully")
        return app
        
    except Exception as e:
        logger.error(f"❌ Failed to create FastAPI application: {e}")
        raise

def main():
    """Main entry point for production server"""
    # Setup logging
    logger = setup_logging()
    logger.info("🚀 Starting Intelligent Reporting Production Server")
    
    # Validate environment
    if not validate_environment():
        logger.error("❌ Environment validation failed")
        sys.exit(1)
    
    # Create application
    try:
        app = create_app()
    except Exception as e:
        logger.error(f"❌ Failed to create application: {e}")
        sys.exit(1)
    
    # Configure server settings
    server_config = {
        "app": app,
        "host": config.server.HOST,
        "port": config.server.PORT,
        "workers": config.server.WORKERS if not config.server.DEBUG else 1,
        "log_level": os.getenv("LOG_LEVEL", "info").lower(),
        "access_log": True,
        "reload": config.server.DEBUG,
        "timeout_keep_alive": 30,
        "limit_concurrency": config.server.MAX_CONCURRENT_REQUESTS,
    }
    
    # Add SSL configuration for production
    if config.is_production:
        ssl_keyfile = os.getenv("SSL_KEYFILE")
        ssl_certfile = os.getenv("SSL_CERTFILE")
        
        if ssl_keyfile and ssl_certfile:
            server_config.update({
                "ssl_keyfile": ssl_keyfile,
                "ssl_certfile": ssl_certfile
            })
            logger.info("🔒 SSL/TLS enabled")
    
    # Print startup information
    logger.info("🌟 Server Configuration:")
    logger.info(f"  • Environment: {config.environment}")
    logger.info(f"  • Host: {config.server.HOST}")
    logger.info(f"  • Port: {config.server.PORT}")
    logger.info(f"  • Workers: {server_config['workers']}")
    logger.info(f"  • Debug: {config.server.DEBUG}")
    logger.info(f"  • Max Concurrent: {config.server.MAX_CONCURRENT_REQUESTS}")
    
    # Start server
    try:
        logger.info("🚀 Starting server...")
        uvicorn.run(**server_config)
    except KeyboardInterrupt:
        logger.info("🛑 Server shutdown requested")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
