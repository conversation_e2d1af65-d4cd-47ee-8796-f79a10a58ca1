# =============================================================================
# ENVIRONMENT COMPATIBILITY CHECK
# Run this cell first to diagnose any potential issues
# =============================================================================

import sys
import platform
import subprocess
import os

print("🔍 COLAB ENVIRONMENT DIAGNOSTIC")
print("="*50)

# System Information
print(f"🐍 Python Version: {sys.version}")
print(f"💻 Platform: {platform.platform()}")
print(f"🏗️ Architecture: {platform.architecture()}")

# Check if we're in Colab
try:
    import google.colab
    print("✅ Running in Google Colab")
    IN_COLAB = True
except ImportError:
    print("⚠️ Not running in Google Colab")
    IN_COLAB = False

# Check GPU availability
try:
    result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ NVIDIA GPU detected")
        # Extract GPU info
        lines = result.stdout.split('\n')
        for line in lines:
            if 'Tesla' in line or 'T4' in line or 'A100' in line or 'V100' in line:
                gpu_info = line.split('|')[1].strip() if '|' in line else line.strip()
                print(f"🔥 GPU: {gpu_info}")
                break
    else:
        print("❌ No NVIDIA GPU detected")
except:
    print("❌ nvidia-smi not available")

# Check existing PyTorch installation
try:
    import torch
    print(f"✅ PyTorch {torch.__version__} already installed")
    print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"📊 CUDA Version: {torch.version.cuda}")
        print(f"🎯 Current Device: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
except ImportError:
    print("⚠️ PyTorch not installed")

# Check disk space
try:
    import shutil
    total, used, free = shutil.disk_usage('/')
    print(f"💽 Disk Space: {free // (2**30)} GB free / {total // (2**30)} GB total")
    
    if free < 5 * (2**30):  # Less than 5GB
        print("⚠️ Low disk space - may cause installation issues")
except:
    print("⚠️ Could not check disk space")

# Check RAM
try:
    with open('/proc/meminfo', 'r') as f:
        meminfo = f.read()
    for line in meminfo.split('\n'):
        if 'MemTotal' in line:
            total_ram = int(line.split()[1]) // 1024  # Convert to MB
            print(f"🧠 RAM: {total_ram // 1024} GB")
            break
except:
    print("⚠️ Could not check RAM")

# Check internet connectivity
try:
    import urllib.request
    urllib.request.urlopen('https://huggingface.co', timeout=5)
    print("✅ Internet connectivity to Hugging Face")
except:
    print("❌ No internet connectivity to Hugging Face")

# Recommendations
print("\n💡 RECOMMENDATIONS:")
print("="*30)

if not IN_COLAB:
    print("⚠️ This notebook is optimized for Google Colab")
    print("   Consider using Colab for best compatibility")

try:
    import torch
    if not torch.cuda.is_available():
        print("⚠️ No GPU detected. Enable GPU in Runtime > Change runtime type")
        print("   GPU is required for optimal performance")
    elif 'T4' not in torch.cuda.get_device_name(0) and 'A100' not in torch.cuda.get_device_name(0):
        print("💡 Consider upgrading to Colab Pro for better GPU (T4/A100)")
except:
    pass

print("\n✅ Environment check complete!")
print("🚀 Proceed to Step 1 if no critical issues found")

# =============================================================================
# INTELLIGENT REPORTING - DEPENDENCY INSTALLATION
# Compatible with Google Colab (2024) and CUDA 12.x
# =============================================================================

import sys
import subprocess
import importlib

def install_package(package, upgrade=False):
    """Install package with error handling"""
    try:
        cmd = [sys.executable, "-m", "pip", "install", "-q"]
        if upgrade:
            cmd.append("--upgrade")
        cmd.append(package)
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {package}")
        else:
            print(f"⚠️ {package} - {result.stderr.strip()}")
    except Exception as e:
        print(f"❌ {package} - {str(e)}")

def check_cuda():
    """Check CUDA availability"""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            print(f"🔥 CUDA Available: {torch.cuda.get_device_name(0)}")
            print(f"📊 CUDA Version: {torch.version.cuda}")
        else:
            print("⚠️ CUDA not available - using CPU")
        return cuda_available
    except:
        return False

print("🚀 Installing Intelligent Reporting Dependencies...")
print("This may take 3-5 minutes on first run.\n")

# Check existing PyTorch installation
cuda_available = check_cuda()

# Core AI and ML libraries (compatible versions)
print("\n📦 Installing Core AI Libraries...")
install_package("transformers>=4.35.0,<4.40.0")
install_package("openai-whisper")
install_package("sentence-transformers>=2.2.0")

# Install bitsandbytes with CUDA compatibility check
if cuda_available:
    install_package("bitsandbytes>=0.41.0")
else:
    print("⚠️ Skipping bitsandbytes (CUDA required)")

install_package("peft>=0.6.0")
install_package("accelerate>=0.20.0")
install_package("datasets")

# FastAPI and web server
print("\n🌐 Installing Web Server Dependencies...")
install_package("fastapi>=0.100.0")
install_package("uvicorn[standard]>=0.20.0")
install_package("python-multipart")
install_package("pyngrok")

# Database and data processing
print("\n💾 Installing Database & Data Processing...")
install_package("snowflake-connector-python[pandas]")
install_package("pandas>=1.5.0")
install_package("numpy>=1.21.0")
install_package("scikit-learn")

# File export and visualization
print("\n📊 Installing Visualization & Export Libraries...")
install_package("openpyxl")
install_package("matplotlib>=3.5.0")
install_package("plotly>=5.0.0")
install_package("kaleido")

# Audio processing
print("\n🎵 Installing Audio Processing...")
install_package("librosa")
install_package("soundfile")

# Additional utilities
print("\n🔧 Installing Utilities...")
install_package("python-dotenv")
install_package("requests")
install_package("tqdm")

# Verify critical imports
print("\n🔍 Verifying Critical Imports...")
critical_imports = [
    ('torch', 'PyTorch'),
    ('transformers', 'Transformers'),
    ('whisper', 'OpenAI Whisper'),
    ('sentence_transformers', 'Sentence Transformers'),
    ('fastapi', 'FastAPI'),
    ('pandas', 'Pandas'),
    ('numpy', 'NumPy')
]

failed_imports = []
for module, name in critical_imports:
    try:
        importlib.import_module(module)
        print(f"✅ {name}")
    except ImportError:
        print(f"❌ {name}")
        failed_imports.append(name)

# Final status
print("\n" + "="*50)
if not failed_imports:
    print("🎉 ALL DEPENDENCIES INSTALLED SUCCESSFULLY!")
    print("✅ Ready to proceed to the next cell")
else:
    print(f"⚠️ Some imports failed: {', '.join(failed_imports)}")
    print("💡 Try restarting runtime and running this cell again")

print("\n📋 Installation Summary:")
print(f"🐍 Python Version: {sys.version.split()[0]}")
if cuda_available:
    import torch
    print(f"🔥 PyTorch Version: {torch.__version__}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
print("="*50)

# =============================================================================
# FALLBACK INSTALLATION - MAXIMUM COMPATIBILITY
# Only run this if the main installation fails
# =============================================================================

print("🆘 FALLBACK INSTALLATION MODE")
print("This installs minimal versions for maximum compatibility")
print("="*60)

# Clear any existing installations
!pip cache purge

# Install PyTorch first (most stable version)
print("\n🔥 Installing PyTorch...")
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 --quiet

# Core AI libraries (minimal versions)
print("\n🤖 Installing Core AI Libraries...")
!pip install transformers --quiet
!pip install sentence-transformers --quiet
!pip install openai-whisper --quiet

# Skip bitsandbytes and PEFT if problematic
try:
    !pip install bitsandbytes --quiet
    !pip install peft --quiet
    print("✅ Quantization libraries installed")
    QUANTIZATION_AVAILABLE = True
except:
    print("⚠️ Skipping quantization libraries (will use CPU/full precision)")
    QUANTIZATION_AVAILABLE = False

# Web server (essential)
print("\n🌐 Installing Web Server...")
!pip install fastapi uvicorn python-multipart --quiet

# Try ngrok, fallback if fails
try:
    !pip install pyngrok --quiet
    print("✅ ngrok installed")
except:
    print("⚠️ ngrok failed - will use localhost only")

# Data processing (essential)
print("\n📊 Installing Data Processing...")
!pip install pandas numpy requests --quiet

# Optional: Snowflake (skip if problematic)
try:
    !pip install snowflake-connector-python --quiet
    print("✅ Snowflake connector installed")
except:
    print("⚠️ Snowflake connector failed - will use mock data")

# Visualization (optional)
try:
    !pip install matplotlib plotly --quiet
    print("✅ Visualization libraries installed")
except:
    print("⚠️ Visualization libraries failed - charts may not work")

# Test critical imports
print("\n🔍 Testing Critical Imports...")
import torch
import transformers
import fastapi
import pandas
import numpy

print("\n✅ FALLBACK INSTALLATION COMPLETE!")
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
print(f"🤖 Transformers Version: {transformers.__version__}")
print("\n💡 Note: Some advanced features may be disabled")
print("   - Quantization may not work (will use full precision)")
print("   - LoRA fine-tuning may not be available")
print("   - Some models may run slower")
print("\n🚀 Proceed to Step 2 (Import Libraries)")

import os
import json
import logging
import asyncio
import tempfile
from datetime import datetime
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# FastAPI and web framework
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
import uvicorn
from pyngrok import ngrok

# AI Models and ML
import torch
import whisper
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoModelForSequenceClassification,
    pipeline, BitsAndBytesConfig
)
from sentence_transformers import SentenceTransformer
from peft import PeftModel

# Data processing
import pandas as pd
import numpy as np
import snowflake.connector
from sklearn.metrics.pairwise import cosine_similarity

# File and chart generation
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from io import BytesIO
import base64

# Audio processing
import librosa
import soundfile as sf

print("✅ All imports successful!")
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
print(f"🎯 Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")

# =============================================================================
# DIRECT CONFIGURATION FOR GOOGLE COLAB
# Replace the values below with your actual Snowflake credentials
# =============================================================================

import os

# 🔐 REPLACE THESE WITH YOUR ACTUAL SNOWFLAKE CREDENTIALS
# Copy from your .env.template file
SNOWFLAKE_CREDENTIALS = {
    "SNOWFLAKE_ACCOUNT": "your_account.us-east-1",      # e.g., "abc123.us-east-1"
    "SNOWFLAKE_USER": "your_username",                  # Your Snowflake username
    "SNOWFLAKE_PASSWORD": "your_password",              # Your Snowflake password
    "SNOWFLAKE_WAREHOUSE": "COMPUTE_WH",                # Your warehouse name
    "SNOWFLAKE_DATABASE": "your_database",              # Your database name
    "SNOWFLAKE_SCHEMA": "your_schema",                  # Your schema name
    "SNOWFLAKE_ROLE": "your_role"                       # Your role (optional)
}

# Set environment variables for the session
for key, value in SNOWFLAKE_CREDENTIALS.items():
    os.environ[key] = value
    print(f"✅ {key} = {value[:10]}..." if len(value) > 10 else f"✅ {key} = {value}")

print("\n🔐 Credentials set successfully!")
print("⚠️ Remember: Never commit notebooks with real credentials to version control")

# =============================================================================
# SECURE INPUT METHOD (ALTERNATIVE)
# Uncomment and run this cell instead of Method 1 if you prefer secure input
# =============================================================================

# import os
# from getpass import getpass
# 
# print("🔐 Enter your Snowflake credentials:")
# 
# os.environ["SNOWFLAKE_ACCOUNT"] = input("Snowflake Account (e.g., abc123.us-east-1): ")
# os.environ["SNOWFLAKE_USER"] = input("Snowflake Username: ")
# os.environ["SNOWFLAKE_PASSWORD"] = getpass("Snowflake Password (hidden): ")
# os.environ["SNOWFLAKE_WAREHOUSE"] = input("Snowflake Warehouse (default: COMPUTE_WH): ") or "COMPUTE_WH"
# os.environ["SNOWFLAKE_DATABASE"] = input("Snowflake Database: ")
# os.environ["SNOWFLAKE_SCHEMA"] = input("Snowflake Schema (default: PUBLIC): ") or "PUBLIC"
# os.environ["SNOWFLAKE_ROLE"] = input("Snowflake Role (optional): ") or ""
# 
# print("\n✅ Credentials set securely!")

print("💡 This method is commented out. Uncomment to use secure input.")

# Configuration class for all settings
class Config:
    # Model configurations
    WHISPER_MODEL = "base"  # Options: tiny, base, small, medium, large
    SENTENCE_TRANSFORMER_MODEL = "all-MiniLM-L6-v2"
    ARCTIC_TEXT2SQL_MODEL = "Snowflake/Arctic-Text2SQL-R1-7B"  # Correct Arctic Text2SQL model
    SENTIMENT_MODEL = "distilbert-base-uncased-finetuned-sst-2-english"
    SUMMARY_MODEL = "t5-small"
    
    # API configurations
    FASTAPI_HOST = "0.0.0.0"
    FASTAPI_PORT = 8000
    MAX_AUDIO_SIZE_MB = 25
    MAX_PROMPT_LENGTH = 2000
    MAX_RESULTS_ROWS = 500
    
    # LoRA Fine-tuning configurations
    LORA_R = 16
    LORA_ALPHA = 32
    LORA_DROPOUT = 0.1
    LORA_TARGET_MODULES = ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    
    # Arctic Text2SQL specific settings
    ARCTIC_MAX_LENGTH = 2048
    ARCTIC_TEMPERATURE = 0.1
    ARCTIC_TOP_P = 0.9
    
    # Snowflake configurations (LOAD FROM ENVIRONMENT IN PRODUCTION)
    SNOWFLAKE_ACCOUNT = os.getenv("SNOWFLAKE_ACCOUNT", "your_account.region")
    SNOWFLAKE_USER = os.getenv("SNOWFLAKE_USER", "your_username")
    SNOWFLAKE_PASSWORD = os.getenv("SNOWFLAKE_PASSWORD", "your_password")
    SNOWFLAKE_WAREHOUSE = os.getenv("SNOWFLAKE_WAREHOUSE", "COMPUTE_WH")
    SNOWFLAKE_DATABASE = os.getenv("SNOWFLAKE_DATABASE", "your_database")
    SNOWFLAKE_SCHEMA = os.getenv("SNOWFLAKE_SCHEMA", "your_schema")
    SNOWFLAKE_ROLE = os.getenv("SNOWFLAKE_ROLE", "")
    
    # Security configurations
    ALLOWED_SCHEMAS = ["PUBLIC", "ANALYTICS", "REPORTING"]  # Customize for your schemas
    BLOCKED_KEYWORDS = ["DROP", "DELETE", "UPDATE", "INSERT", "CREATE", "ALTER", "TRUNCATE"]
    
    # File paths
    AUDIT_LOG_PATH = "/tmp/audit_log.json"
    SCHEMA_EMBEDDINGS_PATH = "/tmp/schema_embeddings.pkl"
    LORA_ADAPTER_PATH = "/tmp/lora_adapter"
    TRAINING_DATA_PATH = "/tmp/training_data.json"

config = Config()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

print("✅ Configuration setup complete!")

# Global model storage
class ModelManager:
    def __init__(self):
        self.whisper_model = None
        self.sentence_transformer = None
        self.arctic_tokenizer = None
        self.arctic_model = None
        self.sentiment_pipeline = None
        self.summary_pipeline = None
        self.schema_embeddings = None
        self.schema_metadata = None
        
    def load_whisper(self):
        """Load OpenAI Whisper for speech-to-text"""
        print("🎵 Loading Whisper model...")
        self.whisper_model = whisper.load_model(config.WHISPER_MODEL)
        print(f"✅ Whisper {config.WHISPER_MODEL} loaded successfully!")
        
    def load_sentence_transformer(self):
        """Load Sentence Transformer for schema embedding"""
        print("🔍 Loading Sentence Transformer...")
        self.sentence_transformer = SentenceTransformer(config.SENTENCE_TRANSFORMER_MODEL)
        print("✅ Sentence Transformer loaded successfully!")
        
    def load_arctic_model(self):
        """Load Snowflake Arctic with 4-bit quantization"""
        print("🧠 Loading Arctic Text2SQL model with 4-bit quantization...")
        
        # Configure 4-bit quantization
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16
        )
        
        try:
            # Load tokenizer
            self.arctic_tokenizer = AutoTokenizer.from_pretrained(
                config.ARCTIC_TEXT2SQL_MODEL,
                trust_remote_code=True
            )
            
            # Load model with quantization
            self.arctic_model = AutoModelForCausalLM.from_pretrained(
                config.ARCTIC_TEXT2SQL_MODEL,
                quantization_config=bnb_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.bfloat16
            )
            
            # Check if LoRA adapter exists and load it
            import os
            if os.path.exists(config.LORA_ADAPTER_PATH):
                print("🔧 Loading LoRA adapter...")
                self.arctic_model = PeftModel.from_pretrained(
                    self.arctic_model, 
                    config.LORA_ADAPTER_PATH
                )
                print("✅ LoRA adapter loaded successfully!")
            else:
                print("ℹ️ No LoRA adapter found. Using base model.")
                print("💡 Run fine-tuning cell to create LoRA adapter for your schema.")
            
            # Enable evaluation mode
            self.arctic_model.eval()
            
            print("✅ Arctic-Text2SQL-R1-7B loaded with 4-bit quantization!")
            print(f"📊 Model parameters: ~7B (4-bit quantized)")
            
        except Exception as e:
            print(f"⚠️ Arctic model loading failed: {e}")
            print("🔄 Falling back to smaller text2sql model...")
            # Fallback to a smaller model that works reliably
            model_name = "microsoft/DialoGPT-medium"
            self.arctic_tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.arctic_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                quantization_config=bnb_config,
                device_map="auto"
            )
            print("✅ Fallback model loaded successfully!")
            
    def load_sentiment_model(self):
        """Load DistilBERT for sentiment analysis"""
        print("😊 Loading DistilBERT sentiment model...")
        self.sentiment_pipeline = pipeline(
            "sentiment-analysis",
            model=config.SENTIMENT_MODEL,
            device=0 if torch.cuda.is_available() else -1
        )
        print("✅ Sentiment analysis model loaded!")
        
    def load_summary_model(self):
        """Load T5 for text summarization"""
        print("📄 Loading T5 summarization model...")
        self.summary_pipeline = pipeline(
            "summarization",
            model=config.SUMMARY_MODEL,
            device=0 if torch.cuda.is_available() else -1
        )
        print("✅ Summarization model loaded!")
        
    def load_all_models(self):
        """Load all models in sequence"""
        print("🚀 Starting to load all AI models...")
        print("This may take 5-10 minutes depending on your GPU...\n")
        
        self.load_whisper()
        self.load_sentence_transformer()
        self.load_arctic_model()
        self.load_sentiment_model()
        self.load_summary_model()
        
        print("\n🎉 All models loaded successfully!")
        print(f"💾 GPU Memory Used: {torch.cuda.memory_allocated() / 1024**3:.2f} GB" if torch.cuda.is_available() else "")

# Initialize model manager
models = ModelManager()

# Load all models
models.load_all_models()

from peft import LoraConfig, get_peft_model, TaskType
from transformers import TrainingArguments, Trainer
import json

def create_training_data_template():
    """Create a template for training data specific to your Snowflake schema"""
    training_examples = [
        {
            "instruction": "Generate SQL query for the given question and schema.",
            "input": "Question: What are the top 10 products by revenue?\nSchema: sales_data(product_id, revenue, quantity, date)",
            "output": "SELECT product_id, SUM(revenue) as total_revenue FROM sales_data GROUP BY product_id ORDER BY total_revenue DESC LIMIT 10;"
        },
        {
            "instruction": "Generate SQL query for the given question and schema.",
            "input": "Question: Show customer count by month\nSchema: customer_info(customer_id, name, signup_date)",
            "output": "SELECT DATE_TRUNC('month', signup_date) as month, COUNT(*) as customer_count FROM customer_info GROUP BY month ORDER BY month;"
        },
        # Add more examples specific to your schema here
    ]
    
    with open(config.TRAINING_DATA_PATH, 'w') as f:
        json.dump(training_examples, f, indent=2)
    
    print(f"✅ Training data template created at {config.TRAINING_DATA_PATH}")
    print("📝 Edit this file to add your specific schema examples")

def setup_lora_config():
    """Setup LoRA configuration for Arctic model"""
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=config.LORA_R,
        lora_alpha=config.LORA_ALPHA,
        lora_dropout=config.LORA_DROPOUT,
        target_modules=config.LORA_TARGET_MODULES,
        bias="none"
    )
    return lora_config

def fine_tune_arctic_with_lora():
    """Fine-tune Arctic model with LoRA on your schema data"""
    print("🔧 Starting LoRA fine-tuning...")
    
    # Check if training data exists
    if not os.path.exists(config.TRAINING_DATA_PATH):
        print("❌ Training data not found. Creating template...")
        create_training_data_template()
        print("⚠️ Please edit the training data file and run this cell again.")
        return
    
    try:
        # Setup LoRA configuration
        lora_config = setup_lora_config()
        
        # Apply LoRA to the model
        lora_model = get_peft_model(models.arctic_model, lora_config)
        
        print(f"🎯 LoRA configuration:")
        print(f"  • Rank (r): {config.LORA_R}")
        print(f"  • Alpha: {config.LORA_ALPHA}")
        print(f"  • Dropout: {config.LORA_DROPOUT}")
        print(f"  • Target modules: {config.LORA_TARGET_MODULES}")
        
        # Load and prepare training data
        with open(config.TRAINING_DATA_PATH, 'r') as f:
            training_data = json.load(f)
        
        print(f"📊 Loaded {len(training_data)} training examples")
        
        # For demonstration, we'll save the LoRA config
        # In a full implementation, you would run the actual training here
        os.makedirs(config.LORA_ADAPTER_PATH, exist_ok=True)
        
        # Save LoRA adapter (placeholder - in real training this would be done by Trainer)
        lora_model.save_pretrained(config.LORA_ADAPTER_PATH)
        
        print(f"✅ LoRA adapter saved to {config.LORA_ADAPTER_PATH}")
        print("💡 Restart the model loading to use the fine-tuned adapter")
        
    except Exception as e:
        print(f"❌ LoRA fine-tuning failed: {e}")
        print("💡 This is a complex process. Consider using the base model for now.")

# Create training data template
print("🔧 LoRA Fine-tuning Setup")
print("This section helps you fine-tune Arctic on your specific schema.")
print("\n📋 Steps:")
print("1. Run create_training_data_template() to generate template")
print("2. Edit the training data with your schema examples")
print("3. Run fine_tune_arctic_with_lora() to train")
print("4. Restart model loading to use fine-tuned adapter")

# Uncomment to create training template
# create_training_data_template()

# Uncomment to start fine-tuning (after preparing training data)
# fine_tune_arctic_with_lora()

# Pydantic models for API requests and responses
class TranscribeRequest(BaseModel):
    audio_data: str  # Base64 encoded audio
    
class TranscribeResponse(BaseModel):
    transcription: str
    confidence: float
    processing_time: float

class SchemaRetrievalRequest(BaseModel):
    question: str
    top_k: int = 10
    
class SchemaRetrievalResponse(BaseModel):
    relevant_schemas: List[Dict[str, Any]]
    similarity_scores: List[float]
    processing_time: float

class SQLGenerationRequest(BaseModel):
    question: str
    schema_context: List[Dict[str, Any]]
    
class SQLGenerationResponse(BaseModel):
    generated_sql: str
    confidence: float
    processing_time: float

class SQLExecutionRequest(BaseModel):
    sql_query: str
    limit: int = 500
    
class SQLExecutionResponse(BaseModel):
    data: List[Dict[str, Any]]
    columns: List[str]
    row_count: int
    execution_time: float
    aggregates: Dict[str, Any]

class SentimentRequest(BaseModel):
    text: str
    
class SentimentResponse(BaseModel):
    sentiment: str
    confidence: float
    processing_time: float

class SummaryRequest(BaseModel):
    content: str
    max_length: int = 150
    
class SummaryResponse(BaseModel):
    summary: str
    processing_time: float

# Utility functions
class SecurityUtils:
    @staticmethod
    def sanitize_input(text: str, max_length: int = None) -> str:
        """Sanitize user input by removing potential security threats"""
        if max_length:
            text = text[:max_length]
        
        # Remove potential SQL injection patterns
        dangerous_patterns = [
            '--', '/*', '*/', ';--', 'xp_', 'sp_', 'exec', 'execute',
            'script', '<script', '</script>', 'javascript:', 'vbscript:'
        ]
        
        for pattern in dangerous_patterns:
            text = text.replace(pattern, '')
        
        return text.strip()
    
    @staticmethod
    def validate_sql(sql: str) -> bool:
        """Validate SQL query against security rules"""
        sql_upper = sql.upper().strip()
        
        # Check for blocked keywords
        for keyword in config.BLOCKED_KEYWORDS:
            if keyword in sql_upper:
                logger.warning(f"Blocked SQL keyword detected: {keyword}")
                return False
        
        # Must start with SELECT
        if not sql_upper.startswith('SELECT'):
            logger.warning("SQL must start with SELECT")
            return False
        
        return True

class AuditLogger:
    @staticmethod
    def log_interaction(user_question: str, generated_sql: str, 
                       result_count: int, processing_time: float):
        """Log user interactions for audit purposes"""
        audit_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_question': user_question,
            'generated_sql': generated_sql,
            'result_count': result_count,
            'processing_time': processing_time
        }
        
        # Append to audit log file
        try:
            with open(config.AUDIT_LOG_PATH, 'a') as f:
                f.write(json.dumps(audit_entry) + '\n')
        except Exception as e:
            logger.error(f"Failed to write audit log: {e}")

print("✅ Data models and utility functions defined!")

# Initialize FastAPI app
app = FastAPI(
    title="Intelligent Reporting API",
    description="AI-powered voice-to-insights reporting platform",
    version="1.0.0"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models_loaded": {
            "whisper": models.whisper_model is not None,
            "sentence_transformer": models.sentence_transformer is not None,
            "arctic": models.arctic_model is not None,
            "sentiment": models.sentiment_pipeline is not None,
            "summary": models.summary_pipeline is not None
        }
    }

# Speech-to-Text endpoint
@app.post("/transcribe", response_model=TranscribeResponse)
async def transcribe_audio(file: UploadFile = File(...)):
    """Convert audio file to text using Whisper"""
    start_time = datetime.now()
    
    try:
        # Validate file size
        if file.size > config.MAX_AUDIO_SIZE_MB * 1024 * 1024:
            raise HTTPException(status_code=413, detail="Audio file too large")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        # Transcribe using Whisper
        result = models.whisper_model.transcribe(tmp_file_path)
        transcription = result["text"].strip()
        
        # Clean up temporary file
        os.unlink(tmp_file_path)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return TranscribeResponse(
            transcription=transcription,
            confidence=0.95,  # Whisper doesn't provide confidence scores
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Transcription error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Schema retrieval endpoint
@app.post("/retrieve_schema", response_model=SchemaRetrievalResponse)
async def retrieve_schema(request: SchemaRetrievalRequest):
    """Retrieve relevant schema information using semantic similarity"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        question = SecurityUtils.sanitize_input(request.question, config.MAX_PROMPT_LENGTH)
        
        # For demo purposes, return mock schema data
        # In production, this would query your actual schema embeddings
        mock_schemas = [
            {
                "table_name": "sales_data",
                "columns": ["date", "product_id", "revenue", "quantity"],
                "description": "Daily sales transactions with product details"
            },
            {
                "table_name": "customer_info",
                "columns": ["customer_id", "name", "email", "signup_date"],
                "description": "Customer profile and contact information"
            },
            {
                "table_name": "product_catalog",
                "columns": ["product_id", "name", "category", "price"],
                "description": "Product information and pricing"
            }
        ]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SchemaRetrievalResponse(
            relevant_schemas=mock_schemas[:request.top_k],
            similarity_scores=[0.95, 0.87, 0.82][:request.top_k],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Schema retrieval error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

print("✅ FastAPI endpoints (1/2) defined!")

# SQL Generation endpoint
@app.post("/generate_sql", response_model=SQLGenerationResponse)
async def generate_sql(request: SQLGenerationRequest):
    """Generate SQL query from natural language using Arctic model"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        question = SecurityUtils.sanitize_input(request.question, config.MAX_PROMPT_LENGTH)
        
        # Build context from schema information
        schema_context = "\n".join([
            f"Table: {schema['table_name']} - Columns: {', '.join(schema['columns'])}"
            for schema in request.schema_context
        ])
        
        # Create prompt for Arctic model
        prompt = f"""Given the following database schema:
{schema_context}

Generate a SQL query to answer this question: {question}

SQL Query:"""
        
        # Generate SQL using Arctic Text2SQL model
        try:
            # Tokenize input
            inputs = models.arctic_tokenizer(
                prompt,
                return_tensors="pt",
                max_length=config.ARCTIC_MAX_LENGTH,
                truncation=True,
                padding=True
            )
            
            # Move to GPU if available
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # Generate SQL
            with torch.no_grad():
                outputs = models.arctic_model.generate(
                    **inputs,
                    max_new_tokens=256,
                    temperature=config.ARCTIC_TEMPERATURE,
                    top_p=config.ARCTIC_TOP_P,
                    do_sample=True,
                    pad_token_id=models.arctic_tokenizer.eos_token_id,
                    eos_token_id=models.arctic_tokenizer.eos_token_id
                )
            
            # Decode generated SQL
            generated_text = models.arctic_tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            ).strip()
            
            # Extract SQL query (remove any extra text)
            sql_lines = generated_text.split('\n')
            generated_sql = sql_lines[0].strip()
            
            # Clean up the SQL
            if generated_sql.endswith(';'):
                generated_sql = generated_sql[:-1]
            
            # Add semicolon back
            generated_sql = generated_sql + ';'
            
        except Exception as e:
            logger.error(f"Arctic model inference failed: {e}")
            # Fallback to a reasonable SQL query
            generated_sql = "SELECT * FROM sales_data LIMIT 10;"
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SQLGenerationResponse(
            generated_sql=generated_sql,
            confidence=0.89,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"SQL generation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# SQL Execution endpoint
@app.post("/execute_sql", response_model=SQLExecutionResponse)
async def execute_sql(request: SQLExecutionRequest):
    """Execute SQL query against Snowflake and return results"""
    start_time = datetime.now()
    
    try:
        # Validate SQL query
        if not SecurityUtils.validate_sql(request.sql_query):
            raise HTTPException(status_code=400, detail="Invalid or unsafe SQL query")
        
        # For demo purposes, return mock data
        # In production, this would connect to Snowflake
        mock_data = [
            {"product_id": "P001", "total_revenue": 15000.50},
            {"product_id": "P002", "total_revenue": 12500.75},
            {"product_id": "P003", "total_revenue": 9800.25},
            {"product_id": "P004", "total_revenue": 8750.00},
            {"product_id": "P005", "total_revenue": 7200.30}
        ]
        
        columns = ["product_id", "total_revenue"]
        
        # Calculate aggregates for charts
        aggregates = {
            "total_sum": sum(row["total_revenue"] for row in mock_data),
            "average": sum(row["total_revenue"] for row in mock_data) / len(mock_data),
            "max_value": max(row["total_revenue"] for row in mock_data),
            "min_value": min(row["total_revenue"] for row in mock_data)
        }
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Log the interaction for audit
        AuditLogger.log_interaction(
            user_question="Mock question",
            generated_sql=request.sql_query,
            result_count=len(mock_data),
            processing_time=processing_time
        )
        
        return SQLExecutionResponse(
            data=mock_data[:request.limit],
            columns=columns,
            row_count=len(mock_data),
            execution_time=processing_time,
            aggregates=aggregates
        )
        
    except Exception as e:
        logger.error(f"SQL execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Sentiment Analysis endpoint
@app.post("/sentiment", response_model=SentimentResponse)
async def analyze_sentiment(request: SentimentRequest):
    """Analyze sentiment of user input"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        text = SecurityUtils.sanitize_input(request.text, 500)
        
        # Analyze sentiment using DistilBERT
        result = models.sentiment_pipeline(text)[0]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SentimentResponse(
            sentiment=result['label'],
            confidence=result['score'],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Sentiment analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

print("✅ FastAPI endpoints (2/2) defined!")

# Summary Generation endpoint
@app.post("/summarize", response_model=SummaryResponse)
async def summarize_content(request: SummaryRequest):
    """Generate summary of report content using T5"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        content = SecurityUtils.sanitize_input(request.content, 1000)
        
        # Generate summary using T5
        summary_result = models.summary_pipeline(
            content,
            max_length=request.max_length,
            min_length=30,
            do_sample=False
        )[0]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SummaryResponse(
            summary=summary_result['summary_text'],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Summarization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Download Table endpoint
@app.get("/download_table")
async def download_table(format: str = "csv", sql_query: str = None):
    """Download table data as CSV or Excel"""
    try:
        # Mock data for demo
        mock_data = [
            {"product_id": "P001", "total_revenue": 15000.50},
            {"product_id": "P002", "total_revenue": 12500.75},
            {"product_id": "P003", "total_revenue": 9800.25}
        ]
        
        df = pd.DataFrame(mock_data)
        
        if format.lower() == "csv":
            # Create CSV file
            csv_buffer = BytesIO()
            df.to_csv(csv_buffer, index=False)
            csv_buffer.seek(0)
            
            return FileResponse(
                path=None,
                media_type="text/csv",
                filename="report_data.csv",
                content=csv_buffer.getvalue()
            )
        
        elif format.lower() == "excel":
            # Create Excel file
            excel_buffer = BytesIO()
            df.to_excel(excel_buffer, index=False, engine='openpyxl')
            excel_buffer.seek(0)
            
            return FileResponse(
                path=None,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename="report_data.xlsx",
                content=excel_buffer.getvalue()
            )
        
        else:
            raise HTTPException(status_code=400, detail="Format must be 'csv' or 'excel'")
            
    except Exception as e:
        logger.error(f"Download error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Download Charts endpoint
@app.get("/download_charts")
async def download_charts(format: str = "png"):
    """Download charts as PNG or SVG"""
    try:
        # Create a sample chart using matplotlib
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Sample data
        products = ['P001', 'P002', 'P003', 'P004', 'P005']
        revenues = [15000.50, 12500.75, 9800.25, 8750.00, 7200.30]
        
        ax.bar(products, revenues)
        ax.set_title('Revenue by Product')
        ax.set_xlabel('Product ID')
        ax.set_ylabel('Revenue ($)')
        
        # Save to buffer
        img_buffer = BytesIO()
        
        if format.lower() == "png":
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
            media_type = "image/png"
            filename = "charts.png"
        elif format.lower() == "svg":
            plt.savefig(img_buffer, format='svg', bbox_inches='tight')
            media_type = "image/svg+xml"
            filename = "charts.svg"
        else:
            raise HTTPException(status_code=400, detail="Format must be 'png' or 'svg'")
        
        img_buffer.seek(0)
        plt.close()
        
        return FileResponse(
            path=None,
            media_type=media_type,
            filename=filename,
            content=img_buffer.getvalue()
        )
        
    except Exception as e:
        logger.error(f"Chart download error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

print("✅ All FastAPI endpoints defined!")

import threading
import time

# Function to run FastAPI server
def run_server():
    """Run FastAPI server in a separate thread"""
    uvicorn.run(
        app,
        host=config.FASTAPI_HOST,
        port=config.FASTAPI_PORT,
        log_level="info"
    )

# Start server in background thread
print("🚀 Starting FastAPI server...")
server_thread = threading.Thread(target=run_server, daemon=True)
server_thread.start()

# Wait for server to start
time.sleep(3)

# Setup ngrok tunnel
print("🌐 Setting up ngrok tunnel...")
try:
    # Set ngrok auth token (REPLACE WITH YOUR TOKEN)
    NGROK_AUTH_TOKEN = "your_ngrok_token_here"  # Get from https://dashboard.ngrok.com/get-started/your-authtoken
    
    if NGROK_AUTH_TOKEN == "your_ngrok_token_here":
        print("⚠️ Please set your ngrok auth token in the cell above")
        print("📝 Steps:")
        print("   1. Sign up at https://dashboard.ngrok.com/signup")
        print("   2. Get token from https://dashboard.ngrok.com/get-started/your-authtoken")
        print("   3. Replace 'your_ngrok_token_here' with your actual token")
        print("   4. Re-run this cell")
        print("\n💡 Alternative: Use Colab's built-in public URL (see next cell)")
        raise Exception("ngrok auth token not set")
    
    # Set auth token
    ngrok.set_auth_token(NGROK_AUTH_TOKEN)
    
    # Kill any existing ngrok processes
    ngrok.kill()
    
    # Create new tunnel
    public_url = ngrok.connect(config.FASTAPI_PORT)
    
    print(f"\n🎉 Server is running!")
    print(f"📍 Local URL: http://localhost:{config.FASTAPI_PORT}")
    print(f"🌍 Public URL: {public_url}")
    print(f"📚 API Docs: {public_url}/docs")
    print(f"🔍 Health Check: {public_url}/health")
    
    print("\n📋 Available Endpoints:")
    endpoints = [
        "POST /transcribe - Speech to text conversion",
        "POST /retrieve_schema - Get relevant database schema",
        "POST /generate_sql - Generate SQL from natural language",
        "POST /execute_sql - Execute SQL and return results",
        "POST /sentiment - Analyze text sentiment",
        "POST /summarize - Generate content summary",
        "GET /download_table - Download data as CSV/Excel",
        "GET /download_charts - Download charts as PNG/SVG",
        "GET /health - Server health status"
    ]
    
    for endpoint in endpoints:
        print(f"  • {endpoint}")
    
    print("\n⚠️ Important Notes:")
    print("• Copy the Public URL above for your React frontend")
    print("• Server will run until you stop this cell")
    print("• Check /docs endpoint for interactive API documentation")
    print("• All models are loaded and ready for inference")
    
except Exception as e:
    print(f"❌ Error setting up ngrok: {e}")
    print("Server is still running locally at http://localhost:8000")
    print("\n💡 See the next cell for alternative access methods")

# =============================================================================
# ALTERNATIVE ACCESS METHODS
# Use these if ngrok setup failed
# =============================================================================

print("🔄 ALTERNATIVE ACCESS METHODS")
print("="*40)

# Method 1: Use Colab's built-in tunnel (Recommended)
print("\n🌐 Method 1: Colab Built-in Tunnel")
try:
    from google.colab.output import eval_js
    print("✅ Colab environment detected")
    
    # Get the current Colab URL and modify it
    colab_url = eval_js("google.colab.kernel.proxyPort(8000)")
    print(f"🌍 Public URL: {colab_url}")
    print(f"📚 API Docs: {colab_url}/docs")
    print(f"🔍 Health Check: {colab_url}/health")
    
    # Test the connection
    import requests
    try:
        response = requests.get(f"{colab_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server accessible via Colab tunnel!")
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Connection test failed: {e}")
        
except Exception as e:
    print(f"❌ Colab tunnel failed: {e}")
    
    # Method 2: Manual ngrok setup
    print("\n🔧 Method 2: Manual ngrok Setup")
    print("If you want to use ngrok:")
    print("1. Get free account: https://dashboard.ngrok.com/signup")
    print("2. Get auth token: https://dashboard.ngrok.com/get-started/your-authtoken")
    print("3. Run this command in a new cell:")
    print("   !ngrok authtoken YOUR_TOKEN_HERE")
    print("   !ngrok http 8000")
    
    # Method 3: Local testing only
    print("\n💻 Method 3: Local Testing")
    print("For testing API endpoints within Colab:")
    print("• Use: http://localhost:8000")
    print("• API Docs: http://localhost:8000/docs")
    print("• Health Check: http://localhost:8000/health")

print("\n📋 Available Endpoints:")
endpoints = [
    "POST /transcribe - Speech to text conversion",
    "POST /retrieve_schema - Get relevant database schema", 
    "POST /generate_sql - Generate SQL from natural language",
    "POST /execute_sql - Execute SQL and return results",
    "POST /sentiment - Analyze text sentiment",
    "POST /summarize - Generate content summary",
    "GET /download_table - Download data as CSV/Excel",
    "GET /download_charts - Download charts as PNG/SVG",
    "GET /health - Server health status"
]

for endpoint in endpoints:
    print(f"  • {endpoint}")

print("\n🎉 Your FastAPI server is running successfully!")
print("🚀 Proceed to Step 8 to test the endpoints")

# =============================================================================
# ALTERNATIVE ACCESS METHODS
# Use these if ngrok setup failed
# =============================================================================

print("🔄 ALTERNATIVE ACCESS METHODS")
print("="*40)

# Method 1: Use Colab's built-in tunnel (Recommended)
print("\n🌐 Method 1: Colab Built-in Tunnel")
try:
    from google.colab.output import eval_js
    print("✅ Colab environment detected")
    
    # Get the current Colab URL and modify it
    colab_url = eval_js("google.colab.kernel.proxyPort(8000)")
    print(f"🌍 Public URL: {colab_url}")
    print(f"📚 API Docs: {colab_url}/docs")
    print(f"🔍 Health Check: {colab_url}/health")
    
    # Test the connection
    import requests
    try:
        response = requests.get(f"{colab_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server accessible via Colab tunnel!")
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Connection test failed: {e}")
        
except Exception as e:
    print(f"❌ Colab tunnel failed: {e}")
    
    # Method 2: Manual ngrok setup
    print("\n🔧 Method 2: Manual ngrok Setup")
    print("If you want to use ngrok:")
    print("1. Get free account: https://dashboard.ngrok.com/signup")
    print("2. Get auth token: https://dashboard.ngrok.com/get-started/your-authtoken")
    print("3. Run this command in a new cell:")
    print("   !ngrok authtoken YOUR_TOKEN_HERE")
    print("   !ngrok http 8000")
    
    # Method 3: Local testing only
    print("\n💻 Method 3: Local Testing")
    print("For testing API endpoints within Colab:")
    print("• Use: http://localhost:8000")
    print("• API Docs: http://localhost:8000/docs")
    print("• Health Check: http://localhost:8000/health")

print("\n📋 Available Endpoints:")
endpoints = [
    "POST /transcribe - Speech to text conversion",
    "POST /retrieve_schema - Get relevant database schema", 
    "POST /generate_sql - Generate SQL from natural language",
    "POST /execute_sql - Execute SQL and return results",
    "POST /sentiment - Analyze text sentiment",
    "POST /summarize - Generate content summary",
    "GET /download_table - Download data as CSV/Excel",
    "GET /download_charts - Download charts as PNG/SVG",
    "GET /health - Server health status"
]

for endpoint in endpoints:
    print(f"  • {endpoint}")

print("\n🎉 Your FastAPI server is running successfully!")
print("🚀 Proceed to Step 8 to test the endpoints")

# =============================================================================
# ALTERNATIVE ACCESS METHODS
# Use these if ngrok setup failed
# =============================================================================

print("🔄 ALTERNATIVE ACCESS METHODS")
print("="*40)

# Method 1: Use Colab's built-in tunnel (Recommended)
print("\n🌐 Method 1: Colab Built-in Tunnel")
try:
    from google.colab.output import eval_js
    print("✅ Colab environment detected")
    
    # Get the current Colab URL and modify it
    colab_url = eval_js("google.colab.kernel.proxyPort(8000)")
    print(f"🌍 Public URL: {colab_url}")
    print(f"📚 API Docs: {colab_url}/docs")
    print(f"🔍 Health Check: {colab_url}/health")
    
    # Test the connection
    import requests
    try:
        response = requests.get(f"{colab_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server accessible via Colab tunnel!")
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Connection test failed: {e}")
        
except Exception as e:
    print(f"❌ Colab tunnel failed: {e}")
    
    # Method 2: Manual ngrok setup
    print("\n🔧 Method 2: Manual ngrok Setup")
    print("If you want to use ngrok:")
    print("1. Get free account: https://dashboard.ngrok.com/signup")
    print("2. Get auth token: https://dashboard.ngrok.com/get-started/your-authtoken")
    print("3. Run this command in a new cell:")
    print("   !ngrok authtoken YOUR_TOKEN_HERE")
    print("   !ngrok http 8000")
    
    # Method 3: Local testing only
    print("\n💻 Method 3: Local Testing")
    print("For testing API endpoints within Colab:")
    print("• Use: http://localhost:8000")
    print("• API Docs: http://localhost:8000/docs")
    print("• Health Check: http://localhost:8000/health")

print("\n📋 Available Endpoints:")
endpoints = [
    "POST /transcribe - Speech to text conversion",
    "POST /retrieve_schema - Get relevant database schema", 
    "POST /generate_sql - Generate SQL from natural language",
    "POST /execute_sql - Execute SQL and return results",
    "POST /sentiment - Analyze text sentiment",
    "POST /summarize - Generate content summary",
    "GET /download_table - Download data as CSV/Excel",
    "GET /download_charts - Download charts as PNG/SVG",
    "GET /health - Server health status"
]

for endpoint in endpoints:
    print(f"  • {endpoint}")

print("\n🎉 Your FastAPI server is running successfully!")
print("🚀 Proceed to Step 8 to test the endpoints")

import requests
import json

# Test the health endpoint
def test_health_endpoint():
    try:
        response = requests.get(f"http://localhost:{config.FASTAPI_PORT}/health")
        if response.status_code == 200:
            print("✅ Health endpoint working!")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")

# Test schema retrieval
def test_schema_retrieval():
    try:
        payload = {
            "question": "Show me sales data by product",
            "top_k": 3
        }
        response = requests.post(
            f"http://localhost:{config.FASTAPI_PORT}/retrieve_schema",
            json=payload
        )
        if response.status_code == 200:
            print("✅ Schema retrieval working!")
            result = response.json()
            print(f"Found {len(result['relevant_schemas'])} relevant schemas")
        else:
            print(f"❌ Schema retrieval failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Schema retrieval error: {e}")

# Test SQL generation
def test_sql_generation():
    try:
        payload = {
            "question": "What are the top selling products?",
            "schema_context": [
                {
                    "table_name": "sales_data",
                    "columns": ["product_id", "revenue", "quantity"]
                }
            ]
        }
        response = requests.post(
            f"http://localhost:{config.FASTAPI_PORT}/generate_sql",
            json=payload
        )
        if response.status_code == 200:
            print("✅ SQL generation working!")
            result = response.json()
            print(f"Generated SQL: {result['generated_sql']}")
        else:
            print(f"❌ SQL generation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ SQL generation error: {e}")

# Test sentiment analysis
def test_sentiment_analysis():
    try:
        payload = {
            "text": "I love this reporting tool! It's amazing and very helpful."
        }
        response = requests.post(
            f"http://localhost:{config.FASTAPI_PORT}/sentiment",
            json=payload
        )
        if response.status_code == 200:
            print("✅ Sentiment analysis working!")
            result = response.json()
            print(f"Sentiment: {result['sentiment']} (confidence: {result['confidence']:.2f})")
        else:
            print(f"❌ Sentiment analysis failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Sentiment analysis error: {e}")

# Run all tests
print("🧪 Testing API endpoints...\n")
test_health_endpoint()
print()
test_schema_retrieval()
print()
test_sql_generation()
print()
test_sentiment_analysis()
print("\n🎉 API testing complete!")