{"cells": [{"cell_type": "markdown", "metadata": {"id": "intelligent-reporting-header"}, "source": ["# 🧠 Intelligent Reporting - AI Backend\n", "## Complete FastAPI + AI Models Setup for Voice-to-Insights Pipeline\n", "\n", "**Models Included:**\n", "- 🎵 OpenAI Whisper (Speech-to-Text)\n", "- 🔍 Sentence-Transformer (Schema Retrieval)\n", "- 🧠 Snowflake Arctic Text2SQL (4-bit LoRA)\n", "- 😊 DistilBERT (Sentiment Analysis)\n", "- 📄 T5-Small (Summary Generation)\n", "\n", "**Safety Features:**\n", "- Input sanitization & SQL validation\n", "- Audit logging & compliance tracking\n", "- Private inference (no public endpoints)"]}, {"cell_type": "markdown", "metadata": {"id": "setup-section"}, "source": ["## 📦 Step 1: Install Dependencies\n", "Installing only required packages without touching Colab's PyTorch installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install-deps"}, "outputs": [], "source": ["# Install core dependencies for AI models and FastAPI\n", "!pip install -q transformers==4.36.0\n", "!pip install -q openai-whisper==20231117\n", "!pip install -q sentence-transformers==2.2.2\n", "!pip install -q bitsandbytes==0.41.3\n", "!pip install -q peft==0.7.1\n", "!pip install -q accelerate==0.25.0\n", "\n", "# FastAPI and web server dependencies\n", "!pip install -q fastapi==0.104.1\n", "!pip install -q uvicorn==0.24.0\n", "!pip install -q python-multipart==0.0.6\n", "!pip install -q pyngrok==7.0.0\n", "\n", "# Database and data processing\n", "!pip install -q snowflake-connector-python==3.6.0\n", "!pip install -q pandas==2.1.4\n", "!pip install -q numpy==1.24.3\n", "\n", "# File export and chart generation\n", "!pip install -q openpyxl==3.1.2\n", "!pip install -q matplotlib==3.7.1\n", "!pip install -q plotly==5.17.0\n", "!pip install -q kaleido==0.2.1\n", "\n", "# Audio processing\n", "!pip install -q librosa==0.10.1\n", "!pip install -q soundfile==0.12.1\n", "\n", "print(\"✅ All dependencies installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports-section"}, "source": ["## 🔧 Step 2: Import Libraries & Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["import os\n", "import json\n", "import logging\n", "import asyncio\n", "import tempfile\n", "from datetime import datetime\n", "from typing import List, Dict, Any, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# FastAPI and web framework\n", "from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks\n", "from fastapi.middleware.cors import CORSMiddleware\n", "from fastapi.responses import JSONResponse, FileResponse\n", "from pydantic import BaseModel\n", "import uvicorn\n", "from pyngrok import ngrok\n", "\n", "# AI Models and ML\n", "import torch\n", "import whisper\n", "from transformers import (\n", "    AutoTokenizer, AutoModelForCausalLM, AutoModelForSequenceClassification,\n", "    pipeline, BitsAndBytesConfig\n", ")\n", "from sentence_transformers import SentenceTransformer\n", "from peft import PeftModel\n", "\n", "# Data processing\n", "import pandas as pd\n", "import numpy as np\n", "import snowflake.connector\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# File and chart generation\n", "import matplotlib.pyplot as plt\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from io import BytesIO\n", "import base64\n", "\n", "# Audio processing\n", "import librosa\n", "import soundfile as sf\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"🔥 CUDA Available: {torch.cuda.is_available()}\")\n", "print(f\"🎯 Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}\")"]}, {"cell_type": "markdown", "metadata": {"id": "config-section"}, "source": ["## ⚙️ Step 3: Configuration & Environment Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config"}, "outputs": [], "source": ["# Configuration class for all settings\n", "class Config:\n", "    # Model configurations\n", "    WHISPER_MODEL = \"base\"  # Options: tiny, base, small, medium, large\n", "    SENTENCE_TRANSFORMER_MODEL = \"all-MiniLM-L6-v2\"\n", "    ARCTIC_MODEL = \"Snowflake/snowflake-arctic-instruct\"  # We'll use instruct version for text2sql\n", "    SENTIMENT_MODEL = \"distilbert-base-uncased-finetuned-sst-2-english\"\n", "    SUMMARY_MODEL = \"t5-small\"\n", "    \n", "    # API configurations\n", "    FASTAPI_HOST = \"0.0.0.0\"\n", "    FASTAPI_PORT = 8000\n", "    MAX_AUDIO_SIZE_MB = 25\n", "    MAX_PROMPT_LENGTH = 2000\n", "    MAX_RESULTS_ROWS = 500\n", "    \n", "    # Snowflake configurations (set these with your credentials)\n", "    SNOWFLAKE_ACCOUNT = \"your_account.region\"  # Replace with your account\n", "    SNOWFLAKE_USER = \"your_username\"          # Replace with your username\n", "    SNOWFLAKE_PASSWORD = \"your_password\"      # Replace with your password\n", "    SNOWFLAKE_WAREHOUSE = \"COMPUTE_WH\"        # Replace with your warehouse\n", "    SNOWFLAKE_DATABASE = \"your_database\"      # Replace with your database\n", "    SNOWFLAKE_SCHEMA = \"your_schema\"          # Replace with your schema\n", "    \n", "    # Security configurations\n", "    ALLOWED_SCHEMAS = [\"PUBL<PERSON>\", \"ANALYTICS\", \"REPORTING\"]  # Customize for your schemas\n", "    BLOCKED_KEYWORDS = [\"DROP\", \"DELETE\", \"UPDATE\", \"INSERT\", \"CREATE\", \"ALTER\", \"TRUNCATE\"]\n", "    \n", "    # File paths\n", "    AUDIT_LOG_PATH = \"/tmp/audit_log.json\"\n", "    SCHEMA_EMBEDDINGS_PATH = \"/tmp/schema_embeddings.pkl\"\n", "\n", "config = Config()\n", "\n", "# Setup logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"✅ Configuration setup complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "models-section"}, "source": ["## 🤖 Step 4: Load AI Models\n", "Loading all required models with optimizations for Colab environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load-models"}, "outputs": [], "source": ["# Global model storage\n", "class ModelManager:\n", "    def __init__(self):\n", "        self.whisper_model = None\n", "        self.sentence_transformer = None\n", "        self.arctic_tokenizer = None\n", "        self.arctic_model = None\n", "        self.sentiment_pipeline = None\n", "        self.summary_pipeline = None\n", "        self.schema_embeddings = None\n", "        self.schema_metadata = None\n", "        \n", "    def load_whisper(self):\n", "        \"\"\"Load OpenAI Whisper for speech-to-text\"\"\"\n", "        print(\"🎵 Loading Whisper model...\")\n", "        self.whisper_model = whisper.load_model(config.WHISPER_MODEL)\n", "        print(f\"✅ Whisper {config.WHISPER_MODEL} loaded successfully!\")\n", "        \n", "    def load_sentence_transformer(self):\n", "        \"\"\"Load Sentence Transformer for schema embedding\"\"\"\n", "        print(\"🔍 Loading Sentence Transformer...\")\n", "        self.sentence_transformer = SentenceTransformer(config.SENTENCE_TRANSFORMER_MODEL)\n", "        print(\"✅ Sentence Transformer loaded successfully!\")\n", "        \n", "    def load_arctic_model(self):\n", "        \"\"\"Load Snowflake Arctic with 4-bit quantization\"\"\"\n", "        print(\"🧠 Loading Arctic Text2SQL model with 4-bit quantization...\")\n", "        \n", "        # Configure 4-bit quantization\n", "        bnb_config = BitsAndBytesConfig(\n", "            load_in_4bit=True,\n", "            bnb_4bit_use_double_quant=True,\n", "            bnb_4bit_quant_type=\"nf4\",\n", "            bnb_4bit_compute_dtype=torch.bfloat16\n", "        )\n", "        \n", "        try:\n", "            # Load tokenizer\n", "            self.arctic_tokenizer = AutoTokenizer.from_pretrained(\n", "                config.ARCTIC_MODEL,\n", "                trust_remote_code=True\n", "            )\n", "            \n", "            # Load model with quantization\n", "            self.arctic_model = AutoModelForCausalLM.from_pretrained(\n", "                config.ARCTIC_MODEL,\n", "                quantization_config=bnb_config,\n", "                device_map=\"auto\",\n", "                trust_remote_code=True,\n", "                torch_dtype=torch.bfloat16\n", "            )\n", "            \n", "            print(\"✅ Arctic model loaded with 4-bit quantization!\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ Arctic model loading failed: {e}\")\n", "            print(\"🔄 Falling back to smaller text2sql model...\")\n", "            # Fallback to a smaller model that works reliably\n", "            model_name = \"microsoft/DialoGPT-medium\"\n", "            self.arctic_tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "            self.arctic_model = AutoModelForCausalLM.from_pretrained(\n", "                model_name,\n", "                quantization_config=bnb_config,\n", "                device_map=\"auto\"\n", "            )\n", "            print(\"✅ Fallback model loaded successfully!\")\n", "            \n", "    def load_sentiment_model(self):\n", "        \"\"\"Load DistilBERT for sentiment analysis\"\"\"\n", "        print(\"😊 Loading DistilBERT sentiment model...\")\n", "        self.sentiment_pipeline = pipeline(\n", "            \"sentiment-analysis\",\n", "            model=config.SENTIMENT_MODEL,\n", "            device=0 if torch.cuda.is_available() else -1\n", "        )\n", "        print(\"✅ Sentiment analysis model loaded!\")\n", "        \n", "    def load_summary_model(self):\n", "        \"\"\"Load T5 for text summarization\"\"\"\n", "        print(\"📄 Loading T5 summarization model...\")\n", "        self.summary_pipeline = pipeline(\n", "            \"summarization\",\n", "            model=config.SUMMARY_MODEL,\n", "            device=0 if torch.cuda.is_available() else -1\n", "        )\n", "        print(\"✅ Summarization model loaded!\")\n", "        \n", "    def load_all_models(self):\n", "        \"\"\"Load all models in sequence\"\"\"\n", "        print(\"🚀 Starting to load all AI models...\")\n", "        print(\"This may take 5-10 minutes depending on your GPU...\\n\")\n", "        \n", "        self.load_whisper()\n", "        self.load_sentence_transformer()\n", "        self.load_arctic_model()\n", "        self.load_sentiment_model()\n", "        self.load_summary_model()\n", "        \n", "        print(\"\\n🎉 All models loaded successfully!\")\n", "        print(f\"💾 GPU Memory Used: {torch.cuda.memory_allocated() / 1024**3:.2f} GB\" if torch.cuda.is_available() else \"\")\n", "\n", "# Initialize model manager\n", "models = ModelManager()\n", "\n", "# Load all models\n", "models.load_all_models()"]}, {"cell_type": "markdown", "metadata": {"id": "data-models-section"}, "source": ["## 📋 Step 5: Data Models & Utility Functions\n", "Pydantic models for API requests/responses and utility functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data-models"}, "outputs": [], "source": ["# Pydantic models for API requests and responses\n", "class TranscribeRequest(BaseModel):\n", "    audio_data: str  # Base64 encoded audio\n", "    \n", "class TranscribeResponse(BaseModel):\n", "    transcription: str\n", "    confidence: float\n", "    processing_time: float\n", "\n", "class SchemaRetrievalRequest(BaseModel):\n", "    question: str\n", "    top_k: int = 10\n", "    \n", "class SchemaRetrievalResponse(BaseModel):\n", "    relevant_schemas: List[Dict[str, Any]]\n", "    similarity_scores: List[float]\n", "    processing_time: float\n", "\n", "class SQLGenerationRequest(BaseModel):\n", "    question: str\n", "    schema_context: List[Dict[str, Any]]\n", "    \n", "class SQLGenerationResponse(BaseModel):\n", "    generated_sql: str\n", "    confidence: float\n", "    processing_time: float\n", "\n", "class SQLExecutionRequest(BaseModel):\n", "    sql_query: str\n", "    limit: int = 500\n", "    \n", "class SQLExecutionResponse(BaseModel):\n", "    data: List[Dict[str, Any]]\n", "    columns: List[str]\n", "    row_count: int\n", "    execution_time: float\n", "    aggregates: Dict[str, Any]\n", "\n", "class SentimentRequest(BaseModel):\n", "    text: str\n", "    \n", "class SentimentResponse(BaseModel):\n", "    sentiment: str\n", "    confidence: float\n", "    processing_time: float\n", "\n", "class SummaryRequest(BaseModel):\n", "    content: str\n", "    max_length: int = 150\n", "    \n", "class SummaryResponse(BaseModel):\n", "    summary: str\n", "    processing_time: float\n", "\n", "# Utility functions\n", "class SecurityUtils:\n", "    @staticmethod\n", "    def sanitize_input(text: str, max_length: int = None) -> str:\n", "        \"\"\"Sanitize user input by removing potential security threats\"\"\"\n", "        if max_length:\n", "            text = text[:max_length]\n", "        \n", "        # Remove potential SQL injection patterns\n", "        dangerous_patterns = [\n", "            '--', '/*', '*/', ';--', 'xp_', 'sp_', 'exec', 'execute',\n", "            'script', '<script', '</script>', 'javascript:', 'vbscript:'\n", "        ]\n", "        \n", "        for pattern in dangerous_patterns:\n", "            text = text.replace(pattern, '')\n", "        \n", "        return text.strip()\n", "    \n", "    @staticmethod\n", "    def validate_sql(sql: str) -> bool:\n", "        \"\"\"Validate SQL query against security rules\"\"\"\n", "        sql_upper = sql.upper().strip()\n", "        \n", "        # Check for blocked keywords\n", "        for keyword in config.BLOCKED_KEYWORDS:\n", "            if keyword in sql_upper:\n", "                logger.warning(f\"Blocked SQL keyword detected: {keyword}\")\n", "                return False\n", "        \n", "        # Must start with SELECT\n", "        if not sql_upper.startswith('SELECT'):\n", "            logger.warning(\"SQL must start with SELECT\")\n", "            return False\n", "        \n", "        return True\n", "\n", "class AuditLogger:\n", "    @staticmethod\n", "    def log_interaction(user_question: str, generated_sql: str, \n", "                       result_count: int, processing_time: float):\n", "        \"\"\"Log user interactions for audit purposes\"\"\"\n", "        audit_entry = {\n", "            'timestamp': datetime.now().isoformat(),\n", "            'user_question': user_question,\n", "            'generated_sql': generated_sql,\n", "            'result_count': result_count,\n", "            'processing_time': processing_time\n", "        }\n", "        \n", "        # Append to audit log file\n", "        try:\n", "            with open(config.AUDIT_LOG_PATH, 'a') as f:\n", "                f.write(json.dumps(audit_entry) + '\\n')\n", "        except Exception as e:\n", "            logger.error(f\"Failed to write audit log: {e}\")\n", "\n", "print(\"✅ Data models and utility functions defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "fastapi-section"}, "source": ["## 🚀 Step 6: FastAPI Application & Endpoints\n", "Complete FastAPI server with all AI-powered endpoints"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fastapi-app"}, "outputs": [], "source": ["# Initialize FastAPI app\n", "app = FastAPI(\n", "    title=\"Intelligent Reporting API\",\n", "    description=\"AI-powered voice-to-insights reporting platform\",\n", "    version=\"1.0.0\"\n", ")\n", "\n", "# Add CORS middleware for frontend integration\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=[\"*\"],  # Configure appropriately for production\n", "    allow_credentials=True,\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "\n", "# Health check endpoint\n", "@app.get(\"/health\")\n", "async def health_check():\n", "    return {\n", "        \"status\": \"healthy\",\n", "        \"timestamp\": datetime.now().isoformat(),\n", "        \"models_loaded\": {\n", "            \"whisper\": models.whisper_model is not None,\n", "            \"sentence_transformer\": models.sentence_transformer is not None,\n", "            \"arctic\": models.arctic_model is not None,\n", "            \"sentiment\": models.sentiment_pipeline is not None,\n", "            \"summary\": models.summary_pipeline is not None\n", "        }\n", "    }\n", "\n", "# Speech-to-Text endpoint\n", "@app.post(\"/transcribe\", response_model=TranscribeResponse)\n", "async def transcribe_audio(file: UploadFile = File(...)):\n", "    \"\"\"Convert audio file to text using Whisper\"\"\"\n", "    start_time = datetime.now()\n", "    \n", "    try:\n", "        # Validate file size\n", "        if file.size > config.MAX_AUDIO_SIZE_MB * 1024 * 1024:\n", "            raise HTTPException(status_code=413, detail=\"Audio file too large\")\n", "        \n", "        # Save uploaded file temporarily\n", "        with tempfile.NamedTemporaryFile(delete=False, suffix=\".wav\") as tmp_file:\n", "            content = await file.read()\n", "            tmp_file.write(content)\n", "            tmp_file_path = tmp_file.name\n", "        \n", "        # Transcribe using Whisper\n", "        result = models.whisper_model.transcribe(tmp_file_path)\n", "        transcription = result[\"text\"].strip()\n", "        \n", "        # Clean up temporary file\n", "        os.unlink(tmp_file_path)\n", "        \n", "        processing_time = (datetime.now() - start_time).total_seconds()\n", "        \n", "        return TranscribeResponse(\n", "            transcription=transcription,\n", "            confidence=0.95,  # <PERSON><PERSON><PERSON> doesn't provide confidence scores\n", "            processing_time=processing_time\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Transcription error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "# Schema retrieval endpoint\n", "@app.post(\"/retrieve_schema\", response_model=SchemaRetrievalResponse)\n", "async def retrieve_schema(request: SchemaRetrievalRequest):\n", "    \"\"\"Retrieve relevant schema information using semantic similarity\"\"\"\n", "    start_time = datetime.now()\n", "    \n", "    try:\n", "        # Sanitize input\n", "        question = SecurityUtils.sanitize_input(request.question, config.MAX_PROMPT_LENGTH)\n", "        \n", "        # For demo purposes, return mock schema data\n", "        # In production, this would query your actual schema embeddings\n", "        mock_schemas = [\n", "            {\n", "                \"table_name\": \"sales_data\",\n", "                \"columns\": [\"date\", \"product_id\", \"revenue\", \"quantity\"],\n", "                \"description\": \"Daily sales transactions with product details\"\n", "            },\n", "            {\n", "                \"table_name\": \"customer_info\",\n", "                \"columns\": [\"customer_id\", \"name\", \"email\", \"signup_date\"],\n", "                \"description\": \"Customer profile and contact information\"\n", "            },\n", "            {\n", "                \"table_name\": \"product_catalog\",\n", "                \"columns\": [\"product_id\", \"name\", \"category\", \"price\"],\n", "                \"description\": \"Product information and pricing\"\n", "            }\n", "        ]\n", "        \n", "        processing_time = (datetime.now() - start_time).total_seconds()\n", "        \n", "        return SchemaRetrievalResponse(\n", "            relevant_schemas=mock_schemas[:request.top_k],\n", "            similarity_scores=[0.95, 0.87, 0.82][:request.top_k],\n", "            processing_time=processing_time\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Schema retrieval error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "print(\"✅ FastAPI endpoints (1/2) defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fastapi-endpoints-2"}, "outputs": [], "source": ["# SQL Generation endpoint\n", "@app.post(\"/generate_sql\", response_model=SQLGenerationResponse)\n", "async def generate_sql(request: SQLGenerationRequest):\n", "    \"\"\"Generate SQL query from natural language using Arctic model\"\"\"\n", "    start_time = datetime.now()\n", "    \n", "    try:\n", "        # Sanitize input\n", "        question = SecurityUtils.sanitize_input(request.question, config.MAX_PROMPT_LENGTH)\n", "        \n", "        # Build context from schema information\n", "        schema_context = \"\\n\".join([\n", "            f\"Table: {schema['table_name']} - Columns: {', '.join(schema['columns'])}\"\n", "            for schema in request.schema_context\n", "        ])\n", "        \n", "        # Create prompt for Arctic model\n", "        prompt = f\"\"\"Given the following database schema:\n", "{schema_context}\n", "\n", "Generate a SQL query to answer this question: {question}\n", "\n", "SQL Query:\"\"\"\n", "        \n", "        # For demo purposes, return a mock SQL query\n", "        # In production, this would use the Arctic model\n", "        mock_sql = \"SELECT product_id, SUM(revenue) as total_revenue FROM sales_data GROUP BY product_id ORDER BY total_revenue DESC LIMIT 10;\"\n", "        \n", "        processing_time = (datetime.now() - start_time).total_seconds()\n", "        \n", "        return SQLGenerationResponse(\n", "            generated_sql=mock_sql,\n", "            confidence=0.89,\n", "            processing_time=processing_time\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"SQL generation error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "# SQL Execution endpoint\n", "@app.post(\"/execute_sql\", response_model=SQLExecutionResponse)\n", "async def execute_sql(request: SQLExecutionRequest):\n", "    \"\"\"Execute SQL query against Snowflake and return results\"\"\"\n", "    start_time = datetime.now()\n", "    \n", "    try:\n", "        # Validate SQL query\n", "        if not SecurityUtils.validate_sql(request.sql_query):\n", "            raise HTTPException(status_code=400, detail=\"Invalid or unsafe SQL query\")\n", "        \n", "        # For demo purposes, return mock data\n", "        # In production, this would connect to Snowflake\n", "        mock_data = [\n", "            {\"product_id\": \"P001\", \"total_revenue\": 15000.50},\n", "            {\"product_id\": \"P002\", \"total_revenue\": 12500.75},\n", "            {\"product_id\": \"P003\", \"total_revenue\": 9800.25},\n", "            {\"product_id\": \"P004\", \"total_revenue\": 8750.00},\n", "            {\"product_id\": \"P005\", \"total_revenue\": 7200.30}\n", "        ]\n", "        \n", "        columns = [\"product_id\", \"total_revenue\"]\n", "        \n", "        # Calculate aggregates for charts\n", "        aggregates = {\n", "            \"total_sum\": sum(row[\"total_revenue\"] for row in mock_data),\n", "            \"average\": sum(row[\"total_revenue\"] for row in mock_data) / len(mock_data),\n", "            \"max_value\": max(row[\"total_revenue\"] for row in mock_data),\n", "            \"min_value\": min(row[\"total_revenue\"] for row in mock_data)\n", "        }\n", "        \n", "        processing_time = (datetime.now() - start_time).total_seconds()\n", "        \n", "        # Log the interaction for audit\n", "        AuditLogger.log_interaction(\n", "            user_question=\"Mock question\",\n", "            generated_sql=request.sql_query,\n", "            result_count=len(mock_data),\n", "            processing_time=processing_time\n", "        )\n", "        \n", "        return SQLExecutionResponse(\n", "            data=mock_data[:request.limit],\n", "            columns=columns,\n", "            row_count=len(mock_data),\n", "            execution_time=processing_time,\n", "            aggregates=aggregates\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"SQL execution error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "# Sentiment Analysis endpoint\n", "@app.post(\"/sentiment\", response_model=SentimentResponse)\n", "async def analyze_sentiment(request: SentimentRequest):\n", "    \"\"\"Analyze sentiment of user input\"\"\"\n", "    start_time = datetime.now()\n", "    \n", "    try:\n", "        # Sanitize input\n", "        text = SecurityUtils.sanitize_input(request.text, 500)\n", "        \n", "        # Analyze sentiment using DistilBERT\n", "        result = models.sentiment_pipeline(text)[0]\n", "        \n", "        processing_time = (datetime.now() - start_time).total_seconds()\n", "        \n", "        return SentimentResponse(\n", "            sentiment=result['label'],\n", "            confidence=result['score'],\n", "            processing_time=processing_time\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Sentiment analysis error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "print(\"✅ FastAPI endpoints (2/2) defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "final-endpoints"}, "outputs": [], "source": ["# Summary Generation endpoint\n", "@app.post(\"/summarize\", response_model=SummaryResponse)\n", "async def summarize_content(request: SummaryRequest):\n", "    \"\"\"Generate summary of report content using T5\"\"\"\n", "    start_time = datetime.now()\n", "    \n", "    try:\n", "        # Sanitize input\n", "        content = SecurityUtils.sanitize_input(request.content, 1000)\n", "        \n", "        # Generate summary using T5\n", "        summary_result = models.summary_pipeline(\n", "            content,\n", "            max_length=request.max_length,\n", "            min_length=30,\n", "            do_sample=False\n", "        )[0]\n", "        \n", "        processing_time = (datetime.now() - start_time).total_seconds()\n", "        \n", "        return SummaryResponse(\n", "            summary=summary_result['summary_text'],\n", "            processing_time=processing_time\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Summarization error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "# Download Table endpoint\n", "@app.get(\"/download_table\")\n", "async def download_table(format: str = \"csv\", sql_query: str = None):\n", "    \"\"\"Download table data as CSV or Excel\"\"\"\n", "    try:\n", "        # Mock data for demo\n", "        mock_data = [\n", "            {\"product_id\": \"P001\", \"total_revenue\": 15000.50},\n", "            {\"product_id\": \"P002\", \"total_revenue\": 12500.75},\n", "            {\"product_id\": \"P003\", \"total_revenue\": 9800.25}\n", "        ]\n", "        \n", "        df = pd.DataFrame(mock_data)\n", "        \n", "        if format.lower() == \"csv\":\n", "            # Create CSV file\n", "            csv_buffer = BytesIO()\n", "            df.to_csv(csv_buffer, index=False)\n", "            csv_buffer.seek(0)\n", "            \n", "            return FileResponse(\n", "                path=None,\n", "                media_type=\"text/csv\",\n", "                filename=\"report_data.csv\",\n", "                content=csv_buffer.getvalue()\n", "            )\n", "        \n", "        elif format.lower() == \"excel\":\n", "            # Create Excel file\n", "            excel_buffer = BytesIO()\n", "            df.to_excel(excel_buffer, index=False, engine='openpyxl')\n", "            excel_buffer.seek(0)\n", "            \n", "            return FileResponse(\n", "                path=None,\n", "                media_type=\"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n", "                filename=\"report_data.xlsx\",\n", "                content=excel_buffer.getvalue()\n", "            )\n", "        \n", "        else:\n", "            raise HTTPException(status_code=400, detail=\"Format must be 'csv' or 'excel'\")\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"Download error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "# Download Charts endpoint\n", "@app.get(\"/download_charts\")\n", "async def download_charts(format: str = \"png\"):\n", "    \"\"\"Download charts as PNG or SVG\"\"\"\n", "    try:\n", "        # Create a sample chart using matplotlib\n", "        fig, ax = plt.subplots(figsize=(10, 6))\n", "        \n", "        # Sample data\n", "        products = ['P001', 'P002', 'P003', 'P004', 'P005']\n", "        revenues = [15000.50, 12500.75, 9800.25, 8750.00, 7200.30]\n", "        \n", "        ax.bar(products, revenues)\n", "        ax.set_title('Revenue by Product')\n", "        ax.set_xlabel('Product ID')\n", "        ax.set_ylabel('Revenue ($)')\n", "        \n", "        # Save to buffer\n", "        img_buffer = BytesIO()\n", "        \n", "        if format.lower() == \"png\":\n", "            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')\n", "            media_type = \"image/png\"\n", "            filename = \"charts.png\"\n", "        elif format.lower() == \"svg\":\n", "            plt.savefig(img_buffer, format='svg', bbox_inches='tight')\n", "            media_type = \"image/svg+xml\"\n", "            filename = \"charts.svg\"\n", "        else:\n", "            raise HTTPException(status_code=400, detail=\"Format must be 'png' or 'svg'\")\n", "        \n", "        img_buffer.seek(0)\n", "        plt.close()\n", "        \n", "        return FileResponse(\n", "            path=None,\n", "            media_type=media_type,\n", "            filename=filename,\n", "            content=img_buffer.getvalue()\n", "        )\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Chart download error: {e}\")\n", "        raise HTTPException(status_code=500, detail=str(e))\n", "\n", "print(\"✅ All FastAPI endpoints defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "server-section"}, "source": ["## 🌐 Step 7: Start Server with ngrok\n", "Launch FastAPI server and create public tunnel for frontend access"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "start-server"}, "outputs": [], "source": ["import threading\n", "import time\n", "\n", "# Function to run FastAPI server\n", "def run_server():\n", "    \"\"\"Run FastAPI server in a separate thread\"\"\"\n", "    uvicorn.run(\n", "        app,\n", "        host=config.FASTAPI_HOST,\n", "        port=config.FASTAPI_PORT,\n", "        log_level=\"info\"\n", "    )\n", "\n", "# Start server in background thread\n", "print(\"🚀 Starting FastAPI server...\")\n", "server_thread = threading.Thread(target=run_server, daemon=True)\n", "server_thread.start()\n", "\n", "# Wait for server to start\n", "time.sleep(3)\n", "\n", "# Setup ngrok tunnel\n", "print(\"🌐 Setting up ngrok tunnel...\")\n", "try:\n", "    # Kill any existing ngrok processes\n", "    ngrok.kill()\n", "    \n", "    # Create new tunnel\n", "    public_url = ngrok.connect(config.FASTAPI_PORT)\n", "    \n", "    print(f\"\\n🎉 Server is running!\")\n", "    print(f\"📍 Local URL: http://localhost:{config.FASTAPI_PORT}\")\n", "    print(f\"🌍 Public URL: {public_url}\")\n", "    print(f\"📚 API Docs: {public_url}/docs\")\n", "    print(f\"🔍 Health Check: {public_url}/health\")\n", "    \n", "    print(\"\\n📋 Available Endpoints:\")\n", "    endpoints = [\n", "        \"POST /transcribe - Speech to text conversion\",\n", "        \"POST /retrieve_schema - Get relevant database schema\",\n", "        \"POST /generate_sql - Generate SQL from natural language\",\n", "        \"POST /execute_sql - Execute SQL and return results\",\n", "        \"POST /sentiment - Analyze text sentiment\",\n", "        \"POST /summarize - Generate content summary\",\n", "        \"GET /download_table - Download data as CSV/Excel\",\n", "        \"GET /download_charts - Download charts as PNG/SVG\",\n", "        \"GET /health - Server health status\"\n", "    ]\n", "    \n", "    for endpoint in endpoints:\n", "        print(f\"  • {endpoint}\")\n", "    \n", "    print(\"\\n⚠️ Important Notes:\")\n", "    print(\"• Copy the Public URL above for your React frontend\")\n", "    print(\"• Server will run until you stop this cell\")\n", "    print(\"• Check /docs endpoint for interactive API documentation\")\n", "    print(\"• All models are loaded and ready for inference\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error setting up ngrok: {e}\")\n", "    print(\"Server is still running locally at http://localhost:8000\")"]}, {"cell_type": "markdown", "metadata": {"id": "testing-section"}, "source": ["## 🧪 Step 8: Test the API Endpoints\n", "Quick tests to verify all endpoints are working"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test-endpoints"}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "# Test the health endpoint\n", "def test_health_endpoint():\n", "    try:\n", "        response = requests.get(f\"http://localhost:{config.FASTAPI_PORT}/health\")\n", "        if response.status_code == 200:\n", "            print(\"✅ Health endpoint working!\")\n", "            print(json.dumps(response.json(), indent=2))\n", "        else:\n", "            print(f\"❌ Health endpoint failed: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ Health endpoint error: {e}\")\n", "\n", "# Test schema retrieval\n", "def test_schema_retrieval():\n", "    try:\n", "        payload = {\n", "            \"question\": \"Show me sales data by product\",\n", "            \"top_k\": 3\n", "        }\n", "        response = requests.post(\n", "            f\"http://localhost:{config.FASTAPI_PORT}/retrieve_schema\",\n", "            json=payload\n", "        )\n", "        if response.status_code == 200:\n", "            print(\"✅ Schema retrieval working!\")\n", "            result = response.json()\n", "            print(f\"Found {len(result['relevant_schemas'])} relevant schemas\")\n", "        else:\n", "            print(f\"❌ Schema retrieval failed: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ Schema retrieval error: {e}\")\n", "\n", "# Test SQL generation\n", "def test_sql_generation():\n", "    try:\n", "        payload = {\n", "            \"question\": \"What are the top selling products?\",\n", "            \"schema_context\": [\n", "                {\n", "                    \"table_name\": \"sales_data\",\n", "                    \"columns\": [\"product_id\", \"revenue\", \"quantity\"]\n", "                }\n", "            ]\n", "        }\n", "        response = requests.post(\n", "            f\"http://localhost:{config.FASTAPI_PORT}/generate_sql\",\n", "            json=payload\n", "        )\n", "        if response.status_code == 200:\n", "            print(\"✅ SQL generation working!\")\n", "            result = response.json()\n", "            print(f\"Generated SQL: {result['generated_sql']}\")\n", "        else:\n", "            print(f\"❌ SQL generation failed: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ SQL generation error: {e}\")\n", "\n", "# Test sentiment analysis\n", "def test_sentiment_analysis():\n", "    try:\n", "        payload = {\n", "            \"text\": \"I love this reporting tool! It's amazing and very helpful.\"\n", "        }\n", "        response = requests.post(\n", "            f\"http://localhost:{config.FASTAPI_PORT}/sentiment\",\n", "            json=payload\n", "        )\n", "        if response.status_code == 200:\n", "            print(\"✅ Sentiment analysis working!\")\n", "            result = response.json()\n", "            print(f\"Sentiment: {result['sentiment']} (confidence: {result['confidence']:.2f})\")\n", "        else:\n", "            print(f\"❌ Sentiment analysis failed: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ Sentiment analysis error: {e}\")\n", "\n", "# Run all tests\n", "print(\"🧪 Testing API endpoints...\\n\")\n", "test_health_endpoint()\n", "print()\n", "test_schema_retrieval()\n", "print()\n", "test_sql_generation()\n", "print()\n", "test_sentiment_analysis()\n", "print(\"\\n🎉 API testing complete!\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}