# Install core dependencies for AI models and FastAPI
!pip install -q transformers==4.36.0
!pip install -q openai-whisper==20231117
!pip install -q sentence-transformers==2.2.2
!pip install -q bitsandbytes==0.41.3
!pip install -q peft==0.7.1
!pip install -q accelerate==0.25.0

# FastAPI and web server dependencies
!pip install -q fastapi==0.104.1
!pip install -q uvicorn==0.24.0
!pip install -q python-multipart==0.0.6
!pip install -q pyngrok==7.0.0

# Database and data processing
!pip install -q snowflake-connector-python==3.6.0
!pip install -q pandas==2.1.4
!pip install -q numpy==1.24.3

# File export and chart generation
!pip install -q openpyxl==3.1.2
!pip install -q matplotlib==3.7.1
!pip install -q plotly==5.17.0
!pip install -q kaleido==0.2.1

# Audio processing
!pip install -q librosa==0.10.1
!pip install -q soundfile==0.12.1

print("✅ All dependencies installed successfully!")

import os
import json
import logging
import asyncio
import tempfile
from datetime import datetime
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# FastAPI and web framework
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
import uvicorn
from pyngrok import ngrok

# AI Models and ML
import torch
import whisper
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoModelForSequenceClassification,
    pipeline, BitsAndBytesConfig
)
from sentence_transformers import SentenceTransformer
from peft import PeftModel

# Data processing
import pandas as pd
import numpy as np
import snowflake.connector
from sklearn.metrics.pairwise import cosine_similarity

# File and chart generation
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from io import BytesIO
import base64

# Audio processing
import librosa
import soundfile as sf

print("✅ All imports successful!")
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
print(f"🎯 Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")

# Configuration class for all settings
class Config:
    # Model configurations
    WHISPER_MODEL = "base"  # Options: tiny, base, small, medium, large
    SENTENCE_TRANSFORMER_MODEL = "all-MiniLM-L6-v2"
    ARCTIC_TEXT2SQL_MODEL = "Snowflake/Arctic-Text2SQL-R1-7B"  # Correct Arctic Text2SQL model
    SENTIMENT_MODEL = "distilbert-base-uncased-finetuned-sst-2-english"
    SUMMARY_MODEL = "t5-small"
    
    # API configurations
    FASTAPI_HOST = "0.0.0.0"
    FASTAPI_PORT = 8000
    MAX_AUDIO_SIZE_MB = 25
    MAX_PROMPT_LENGTH = 2000
    MAX_RESULTS_ROWS = 500
    
    # LoRA Fine-tuning configurations
    LORA_R = 16
    LORA_ALPHA = 32
    LORA_DROPOUT = 0.1
    LORA_TARGET_MODULES = ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    
    # Arctic Text2SQL specific settings
    ARCTIC_MAX_LENGTH = 2048
    ARCTIC_TEMPERATURE = 0.1
    ARCTIC_TOP_P = 0.9
    
    # Snowflake configurations (LOAD FROM ENVIRONMENT IN PRODUCTION)
    SNOWFLAKE_ACCOUNT = os.getenv("SNOWFLAKE_ACCOUNT", "your_account.region")
    SNOWFLAKE_USER = os.getenv("SNOWFLAKE_USER", "your_username")
    SNOWFLAKE_PASSWORD = os.getenv("SNOWFLAKE_PASSWORD", "your_password")
    SNOWFLAKE_WAREHOUSE = os.getenv("SNOWFLAKE_WAREHOUSE", "COMPUTE_WH")
    SNOWFLAKE_DATABASE = os.getenv("SNOWFLAKE_DATABASE", "your_database")
    SNOWFLAKE_SCHEMA = os.getenv("SNOWFLAKE_SCHEMA", "your_schema")
    SNOWFLAKE_ROLE = os.getenv("SNOWFLAKE_ROLE", "")
    
    # Security configurations
    ALLOWED_SCHEMAS = ["PUBLIC", "ANALYTICS", "REPORTING"]  # Customize for your schemas
    BLOCKED_KEYWORDS = ["DROP", "DELETE", "UPDATE", "INSERT", "CREATE", "ALTER", "TRUNCATE"]
    
    # File paths
    AUDIT_LOG_PATH = "/tmp/audit_log.json"
    SCHEMA_EMBEDDINGS_PATH = "/tmp/schema_embeddings.pkl"
    LORA_ADAPTER_PATH = "/tmp/lora_adapter"
    TRAINING_DATA_PATH = "/tmp/training_data.json"

config = Config()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

print("✅ Configuration setup complete!")

# Global model storage
class ModelManager:
    def __init__(self):
        self.whisper_model = None
        self.sentence_transformer = None
        self.arctic_tokenizer = None
        self.arctic_model = None
        self.sentiment_pipeline = None
        self.summary_pipeline = None
        self.schema_embeddings = None
        self.schema_metadata = None
        
    def load_whisper(self):
        """Load OpenAI Whisper for speech-to-text"""
        print("🎵 Loading Whisper model...")
        self.whisper_model = whisper.load_model(config.WHISPER_MODEL)
        print(f"✅ Whisper {config.WHISPER_MODEL} loaded successfully!")
        
    def load_sentence_transformer(self):
        """Load Sentence Transformer for schema embedding"""
        print("🔍 Loading Sentence Transformer...")
        self.sentence_transformer = SentenceTransformer(config.SENTENCE_TRANSFORMER_MODEL)
        print("✅ Sentence Transformer loaded successfully!")
        
    def load_arctic_model(self):
        """Load Snowflake Arctic with 4-bit quantization"""
        print("🧠 Loading Arctic Text2SQL model with 4-bit quantization...")
        
        # Configure 4-bit quantization
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16
        )
        
        try:
            # Load tokenizer
            self.arctic_tokenizer = AutoTokenizer.from_pretrained(
                config.ARCTIC_TEXT2SQL_MODEL,
                trust_remote_code=True
            )
            
            # Load model with quantization
            self.arctic_model = AutoModelForCausalLM.from_pretrained(
                config.ARCTIC_TEXT2SQL_MODEL,
                quantization_config=bnb_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.bfloat16
            )
            
            # Check if LoRA adapter exists and load it
            import os
            if os.path.exists(config.LORA_ADAPTER_PATH):
                print("🔧 Loading LoRA adapter...")
                self.arctic_model = PeftModel.from_pretrained(
                    self.arctic_model, 
                    config.LORA_ADAPTER_PATH
                )
                print("✅ LoRA adapter loaded successfully!")
            else:
                print("ℹ️ No LoRA adapter found. Using base model.")
                print("💡 Run fine-tuning cell to create LoRA adapter for your schema.")
            
            # Enable evaluation mode
            self.arctic_model.eval()
            
            print("✅ Arctic-Text2SQL-R1-7B loaded with 4-bit quantization!")
            print(f"📊 Model parameters: ~7B (4-bit quantized)")
            
        except Exception as e:
            print(f"⚠️ Arctic model loading failed: {e}")
            print("🔄 Falling back to smaller text2sql model...")
            # Fallback to a smaller model that works reliably
            model_name = "microsoft/DialoGPT-medium"
            self.arctic_tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.arctic_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                quantization_config=bnb_config,
                device_map="auto"
            )
            print("✅ Fallback model loaded successfully!")
            
    def load_sentiment_model(self):
        """Load DistilBERT for sentiment analysis"""
        print("😊 Loading DistilBERT sentiment model...")
        self.sentiment_pipeline = pipeline(
            "sentiment-analysis",
            model=config.SENTIMENT_MODEL,
            device=0 if torch.cuda.is_available() else -1
        )
        print("✅ Sentiment analysis model loaded!")
        
    def load_summary_model(self):
        """Load T5 for text summarization"""
        print("📄 Loading T5 summarization model...")
        self.summary_pipeline = pipeline(
            "summarization",
            model=config.SUMMARY_MODEL,
            device=0 if torch.cuda.is_available() else -1
        )
        print("✅ Summarization model loaded!")
        
    def load_all_models(self):
        """Load all models in sequence"""
        print("🚀 Starting to load all AI models...")
        print("This may take 5-10 minutes depending on your GPU...\n")
        
        self.load_whisper()
        self.load_sentence_transformer()
        self.load_arctic_model()
        self.load_sentiment_model()
        self.load_summary_model()
        
        print("\n🎉 All models loaded successfully!")
        print(f"💾 GPU Memory Used: {torch.cuda.memory_allocated() / 1024**3:.2f} GB" if torch.cuda.is_available() else "")

# Initialize model manager
models = ModelManager()

# Load all models
models.load_all_models()

from peft import LoraConfig, get_peft_model, TaskType
from transformers import TrainingArguments, Trainer
import json

def create_training_data_template():
    """Create a template for training data specific to your Snowflake schema"""
    training_examples = [
        {
            "instruction": "Generate SQL query for the given question and schema.",
            "input": "Question: What are the top 10 products by revenue?\nSchema: sales_data(product_id, revenue, quantity, date)",
            "output": "SELECT product_id, SUM(revenue) as total_revenue FROM sales_data GROUP BY product_id ORDER BY total_revenue DESC LIMIT 10;"
        },
        {
            "instruction": "Generate SQL query for the given question and schema.",
            "input": "Question: Show customer count by month\nSchema: customer_info(customer_id, name, signup_date)",
            "output": "SELECT DATE_TRUNC('month', signup_date) as month, COUNT(*) as customer_count FROM customer_info GROUP BY month ORDER BY month;"
        },
        # Add more examples specific to your schema here
    ]
    
    with open(config.TRAINING_DATA_PATH, 'w') as f:
        json.dump(training_examples, f, indent=2)
    
    print(f"✅ Training data template created at {config.TRAINING_DATA_PATH}")
    print("📝 Edit this file to add your specific schema examples")

def setup_lora_config():
    """Setup LoRA configuration for Arctic model"""
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=config.LORA_R,
        lora_alpha=config.LORA_ALPHA,
        lora_dropout=config.LORA_DROPOUT,
        target_modules=config.LORA_TARGET_MODULES,
        bias="none"
    )
    return lora_config

def fine_tune_arctic_with_lora():
    """Fine-tune Arctic model with LoRA on your schema data"""
    print("🔧 Starting LoRA fine-tuning...")
    
    # Check if training data exists
    if not os.path.exists(config.TRAINING_DATA_PATH):
        print("❌ Training data not found. Creating template...")
        create_training_data_template()
        print("⚠️ Please edit the training data file and run this cell again.")
        return
    
    try:
        # Setup LoRA configuration
        lora_config = setup_lora_config()
        
        # Apply LoRA to the model
        lora_model = get_peft_model(models.arctic_model, lora_config)
        
        print(f"🎯 LoRA configuration:")
        print(f"  • Rank (r): {config.LORA_R}")
        print(f"  • Alpha: {config.LORA_ALPHA}")
        print(f"  • Dropout: {config.LORA_DROPOUT}")
        print(f"  • Target modules: {config.LORA_TARGET_MODULES}")
        
        # Load and prepare training data
        with open(config.TRAINING_DATA_PATH, 'r') as f:
            training_data = json.load(f)
        
        print(f"📊 Loaded {len(training_data)} training examples")
        
        # For demonstration, we'll save the LoRA config
        # In a full implementation, you would run the actual training here
        os.makedirs(config.LORA_ADAPTER_PATH, exist_ok=True)
        
        # Save LoRA adapter (placeholder - in real training this would be done by Trainer)
        lora_model.save_pretrained(config.LORA_ADAPTER_PATH)
        
        print(f"✅ LoRA adapter saved to {config.LORA_ADAPTER_PATH}")
        print("💡 Restart the model loading to use the fine-tuned adapter")
        
    except Exception as e:
        print(f"❌ LoRA fine-tuning failed: {e}")
        print("💡 This is a complex process. Consider using the base model for now.")

# Create training data template
print("🔧 LoRA Fine-tuning Setup")
print("This section helps you fine-tune Arctic on your specific schema.")
print("\n📋 Steps:")
print("1. Run create_training_data_template() to generate template")
print("2. Edit the training data with your schema examples")
print("3. Run fine_tune_arctic_with_lora() to train")
print("4. Restart model loading to use fine-tuned adapter")

# Uncomment to create training template
# create_training_data_template()

# Uncomment to start fine-tuning (after preparing training data)
# fine_tune_arctic_with_lora()

# Pydantic models for API requests and responses
class TranscribeRequest(BaseModel):
    audio_data: str  # Base64 encoded audio
    
class TranscribeResponse(BaseModel):
    transcription: str
    confidence: float
    processing_time: float

class SchemaRetrievalRequest(BaseModel):
    question: str
    top_k: int = 10
    
class SchemaRetrievalResponse(BaseModel):
    relevant_schemas: List[Dict[str, Any]]
    similarity_scores: List[float]
    processing_time: float

class SQLGenerationRequest(BaseModel):
    question: str
    schema_context: List[Dict[str, Any]]
    
class SQLGenerationResponse(BaseModel):
    generated_sql: str
    confidence: float
    processing_time: float

class SQLExecutionRequest(BaseModel):
    sql_query: str
    limit: int = 500
    
class SQLExecutionResponse(BaseModel):
    data: List[Dict[str, Any]]
    columns: List[str]
    row_count: int
    execution_time: float
    aggregates: Dict[str, Any]

class SentimentRequest(BaseModel):
    text: str
    
class SentimentResponse(BaseModel):
    sentiment: str
    confidence: float
    processing_time: float

class SummaryRequest(BaseModel):
    content: str
    max_length: int = 150
    
class SummaryResponse(BaseModel):
    summary: str
    processing_time: float

# Utility functions
class SecurityUtils:
    @staticmethod
    def sanitize_input(text: str, max_length: int = None) -> str:
        """Sanitize user input by removing potential security threats"""
        if max_length:
            text = text[:max_length]
        
        # Remove potential SQL injection patterns
        dangerous_patterns = [
            '--', '/*', '*/', ';--', 'xp_', 'sp_', 'exec', 'execute',
            'script', '<script', '</script>', 'javascript:', 'vbscript:'
        ]
        
        for pattern in dangerous_patterns:
            text = text.replace(pattern, '')
        
        return text.strip()
    
    @staticmethod
    def validate_sql(sql: str) -> bool:
        """Validate SQL query against security rules"""
        sql_upper = sql.upper().strip()
        
        # Check for blocked keywords
        for keyword in config.BLOCKED_KEYWORDS:
            if keyword in sql_upper:
                logger.warning(f"Blocked SQL keyword detected: {keyword}")
                return False
        
        # Must start with SELECT
        if not sql_upper.startswith('SELECT'):
            logger.warning("SQL must start with SELECT")
            return False
        
        return True

class AuditLogger:
    @staticmethod
    def log_interaction(user_question: str, generated_sql: str, 
                       result_count: int, processing_time: float):
        """Log user interactions for audit purposes"""
        audit_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_question': user_question,
            'generated_sql': generated_sql,
            'result_count': result_count,
            'processing_time': processing_time
        }
        
        # Append to audit log file
        try:
            with open(config.AUDIT_LOG_PATH, 'a') as f:
                f.write(json.dumps(audit_entry) + '\n')
        except Exception as e:
            logger.error(f"Failed to write audit log: {e}")

print("✅ Data models and utility functions defined!")

# Initialize FastAPI app
app = FastAPI(
    title="Intelligent Reporting API",
    description="AI-powered voice-to-insights reporting platform",
    version="1.0.0"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models_loaded": {
            "whisper": models.whisper_model is not None,
            "sentence_transformer": models.sentence_transformer is not None,
            "arctic": models.arctic_model is not None,
            "sentiment": models.sentiment_pipeline is not None,
            "summary": models.summary_pipeline is not None
        }
    }

# Speech-to-Text endpoint
@app.post("/transcribe", response_model=TranscribeResponse)
async def transcribe_audio(file: UploadFile = File(...)):
    """Convert audio file to text using Whisper"""
    start_time = datetime.now()
    
    try:
        # Validate file size
        if file.size > config.MAX_AUDIO_SIZE_MB * 1024 * 1024:
            raise HTTPException(status_code=413, detail="Audio file too large")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        # Transcribe using Whisper
        result = models.whisper_model.transcribe(tmp_file_path)
        transcription = result["text"].strip()
        
        # Clean up temporary file
        os.unlink(tmp_file_path)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return TranscribeResponse(
            transcription=transcription,
            confidence=0.95,  # Whisper doesn't provide confidence scores
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Transcription error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Schema retrieval endpoint
@app.post("/retrieve_schema", response_model=SchemaRetrievalResponse)
async def retrieve_schema(request: SchemaRetrievalRequest):
    """Retrieve relevant schema information using semantic similarity"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        question = SecurityUtils.sanitize_input(request.question, config.MAX_PROMPT_LENGTH)
        
        # For demo purposes, return mock schema data
        # In production, this would query your actual schema embeddings
        mock_schemas = [
            {
                "table_name": "sales_data",
                "columns": ["date", "product_id", "revenue", "quantity"],
                "description": "Daily sales transactions with product details"
            },
            {
                "table_name": "customer_info",
                "columns": ["customer_id", "name", "email", "signup_date"],
                "description": "Customer profile and contact information"
            },
            {
                "table_name": "product_catalog",
                "columns": ["product_id", "name", "category", "price"],
                "description": "Product information and pricing"
            }
        ]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SchemaRetrievalResponse(
            relevant_schemas=mock_schemas[:request.top_k],
            similarity_scores=[0.95, 0.87, 0.82][:request.top_k],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Schema retrieval error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

print("✅ FastAPI endpoints (1/2) defined!")

# SQL Generation endpoint
@app.post("/generate_sql", response_model=SQLGenerationResponse)
async def generate_sql(request: SQLGenerationRequest):
    """Generate SQL query from natural language using Arctic model"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        question = SecurityUtils.sanitize_input(request.question, config.MAX_PROMPT_LENGTH)
        
        # Build context from schema information
        schema_context = "\n".join([
            f"Table: {schema['table_name']} - Columns: {', '.join(schema['columns'])}"
            for schema in request.schema_context
        ])
        
        # Create prompt for Arctic model
        prompt = f"""Given the following database schema:
{schema_context}

Generate a SQL query to answer this question: {question}

SQL Query:"""
        
        # Generate SQL using Arctic Text2SQL model
        try:
            # Tokenize input
            inputs = models.arctic_tokenizer(
                prompt,
                return_tensors="pt",
                max_length=config.ARCTIC_MAX_LENGTH,
                truncation=True,
                padding=True
            )
            
            # Move to GPU if available
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # Generate SQL
            with torch.no_grad():
                outputs = models.arctic_model.generate(
                    **inputs,
                    max_new_tokens=256,
                    temperature=config.ARCTIC_TEMPERATURE,
                    top_p=config.ARCTIC_TOP_P,
                    do_sample=True,
                    pad_token_id=models.arctic_tokenizer.eos_token_id,
                    eos_token_id=models.arctic_tokenizer.eos_token_id
                )
            
            # Decode generated SQL
            generated_text = models.arctic_tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            ).strip()
            
            # Extract SQL query (remove any extra text)
            sql_lines = generated_text.split('\n')
            generated_sql = sql_lines[0].strip()
            
            # Clean up the SQL
            if generated_sql.endswith(';'):
                generated_sql = generated_sql[:-1]
            
            # Add semicolon back
            generated_sql = generated_sql + ';'
            
        except Exception as e:
            logger.error(f"Arctic model inference failed: {e}")
            # Fallback to a reasonable SQL query
            generated_sql = "SELECT * FROM sales_data LIMIT 10;"
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SQLGenerationResponse(
            generated_sql=generated_sql,
            confidence=0.89,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"SQL generation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# SQL Execution endpoint
@app.post("/execute_sql", response_model=SQLExecutionResponse)
async def execute_sql(request: SQLExecutionRequest):
    """Execute SQL query against Snowflake and return results"""
    start_time = datetime.now()
    
    try:
        # Validate SQL query
        if not SecurityUtils.validate_sql(request.sql_query):
            raise HTTPException(status_code=400, detail="Invalid or unsafe SQL query")
        
        # For demo purposes, return mock data
        # In production, this would connect to Snowflake
        mock_data = [
            {"product_id": "P001", "total_revenue": 15000.50},
            {"product_id": "P002", "total_revenue": 12500.75},
            {"product_id": "P003", "total_revenue": 9800.25},
            {"product_id": "P004", "total_revenue": 8750.00},
            {"product_id": "P005", "total_revenue": 7200.30}
        ]
        
        columns = ["product_id", "total_revenue"]
        
        # Calculate aggregates for charts
        aggregates = {
            "total_sum": sum(row["total_revenue"] for row in mock_data),
            "average": sum(row["total_revenue"] for row in mock_data) / len(mock_data),
            "max_value": max(row["total_revenue"] for row in mock_data),
            "min_value": min(row["total_revenue"] for row in mock_data)
        }
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Log the interaction for audit
        AuditLogger.log_interaction(
            user_question="Mock question",
            generated_sql=request.sql_query,
            result_count=len(mock_data),
            processing_time=processing_time
        )
        
        return SQLExecutionResponse(
            data=mock_data[:request.limit],
            columns=columns,
            row_count=len(mock_data),
            execution_time=processing_time,
            aggregates=aggregates
        )
        
    except Exception as e:
        logger.error(f"SQL execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Sentiment Analysis endpoint
@app.post("/sentiment", response_model=SentimentResponse)
async def analyze_sentiment(request: SentimentRequest):
    """Analyze sentiment of user input"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        text = SecurityUtils.sanitize_input(request.text, 500)
        
        # Analyze sentiment using DistilBERT
        result = models.sentiment_pipeline(text)[0]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SentimentResponse(
            sentiment=result['label'],
            confidence=result['score'],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Sentiment analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

print("✅ FastAPI endpoints (2/2) defined!")

# Summary Generation endpoint
@app.post("/summarize", response_model=SummaryResponse)
async def summarize_content(request: SummaryRequest):
    """Generate summary of report content using T5"""
    start_time = datetime.now()
    
    try:
        # Sanitize input
        content = SecurityUtils.sanitize_input(request.content, 1000)
        
        # Generate summary using T5
        summary_result = models.summary_pipeline(
            content,
            max_length=request.max_length,
            min_length=30,
            do_sample=False
        )[0]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SummaryResponse(
            summary=summary_result['summary_text'],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Summarization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Download Table endpoint
@app.get("/download_table")
async def download_table(format: str = "csv", sql_query: str = None):
    """Download table data as CSV or Excel"""
    try:
        # Mock data for demo
        mock_data = [
            {"product_id": "P001", "total_revenue": 15000.50},
            {"product_id": "P002", "total_revenue": 12500.75},
            {"product_id": "P003", "total_revenue": 9800.25}
        ]
        
        df = pd.DataFrame(mock_data)
        
        if format.lower() == "csv":
            # Create CSV file
            csv_buffer = BytesIO()
            df.to_csv(csv_buffer, index=False)
            csv_buffer.seek(0)
            
            return FileResponse(
                path=None,
                media_type="text/csv",
                filename="report_data.csv",
                content=csv_buffer.getvalue()
            )
        
        elif format.lower() == "excel":
            # Create Excel file
            excel_buffer = BytesIO()
            df.to_excel(excel_buffer, index=False, engine='openpyxl')
            excel_buffer.seek(0)
            
            return FileResponse(
                path=None,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename="report_data.xlsx",
                content=excel_buffer.getvalue()
            )
        
        else:
            raise HTTPException(status_code=400, detail="Format must be 'csv' or 'excel'")
            
    except Exception as e:
        logger.error(f"Download error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Download Charts endpoint
@app.get("/download_charts")
async def download_charts(format: str = "png"):
    """Download charts as PNG or SVG"""
    try:
        # Create a sample chart using matplotlib
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Sample data
        products = ['P001', 'P002', 'P003', 'P004', 'P005']
        revenues = [15000.50, 12500.75, 9800.25, 8750.00, 7200.30]
        
        ax.bar(products, revenues)
        ax.set_title('Revenue by Product')
        ax.set_xlabel('Product ID')
        ax.set_ylabel('Revenue ($)')
        
        # Save to buffer
        img_buffer = BytesIO()
        
        if format.lower() == "png":
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
            media_type = "image/png"
            filename = "charts.png"
        elif format.lower() == "svg":
            plt.savefig(img_buffer, format='svg', bbox_inches='tight')
            media_type = "image/svg+xml"
            filename = "charts.svg"
        else:
            raise HTTPException(status_code=400, detail="Format must be 'png' or 'svg'")
        
        img_buffer.seek(0)
        plt.close()
        
        return FileResponse(
            path=None,
            media_type=media_type,
            filename=filename,
            content=img_buffer.getvalue()
        )
        
    except Exception as e:
        logger.error(f"Chart download error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

print("✅ All FastAPI endpoints defined!")

import threading
import time

# Function to run FastAPI server
def run_server():
    """Run FastAPI server in a separate thread"""
    uvicorn.run(
        app,
        host=config.FASTAPI_HOST,
        port=config.FASTAPI_PORT,
        log_level="info"
    )

# Start server in background thread
print("🚀 Starting FastAPI server...")
server_thread = threading.Thread(target=run_server, daemon=True)
server_thread.start()

# Wait for server to start
time.sleep(3)

# Setup ngrok tunnel
print("🌐 Setting up ngrok tunnel...")
try:
    # Kill any existing ngrok processes
    ngrok.kill()
    
    # Create new tunnel
    public_url = ngrok.connect(config.FASTAPI_PORT)
    
    print(f"\n🎉 Server is running!")
    print(f"📍 Local URL: http://localhost:{config.FASTAPI_PORT}")
    print(f"🌍 Public URL: {public_url}")
    print(f"📚 API Docs: {public_url}/docs")
    print(f"🔍 Health Check: {public_url}/health")
    
    print("\n📋 Available Endpoints:")
    endpoints = [
        "POST /transcribe - Speech to text conversion",
        "POST /retrieve_schema - Get relevant database schema",
        "POST /generate_sql - Generate SQL from natural language",
        "POST /execute_sql - Execute SQL and return results",
        "POST /sentiment - Analyze text sentiment",
        "POST /summarize - Generate content summary",
        "GET /download_table - Download data as CSV/Excel",
        "GET /download_charts - Download charts as PNG/SVG",
        "GET /health - Server health status"
    ]
    
    for endpoint in endpoints:
        print(f"  • {endpoint}")
    
    print("\n⚠️ Important Notes:")
    print("• Copy the Public URL above for your React frontend")
    print("• Server will run until you stop this cell")
    print("• Check /docs endpoint for interactive API documentation")
    print("• All models are loaded and ready for inference")
    
except Exception as e:
    print(f"❌ Error setting up ngrok: {e}")
    print("Server is still running locally at http://localhost:8000")

import requests
import json

# Test the health endpoint
def test_health_endpoint():
    try:
        response = requests.get(f"http://localhost:{config.FASTAPI_PORT}/health")
        if response.status_code == 200:
            print("✅ Health endpoint working!")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")

# Test schema retrieval
def test_schema_retrieval():
    try:
        payload = {
            "question": "Show me sales data by product",
            "top_k": 3
        }
        response = requests.post(
            f"http://localhost:{config.FASTAPI_PORT}/retrieve_schema",
            json=payload
        )
        if response.status_code == 200:
            print("✅ Schema retrieval working!")
            result = response.json()
            print(f"Found {len(result['relevant_schemas'])} relevant schemas")
        else:
            print(f"❌ Schema retrieval failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Schema retrieval error: {e}")

# Test SQL generation
def test_sql_generation():
    try:
        payload = {
            "question": "What are the top selling products?",
            "schema_context": [
                {
                    "table_name": "sales_data",
                    "columns": ["product_id", "revenue", "quantity"]
                }
            ]
        }
        response = requests.post(
            f"http://localhost:{config.FASTAPI_PORT}/generate_sql",
            json=payload
        )
        if response.status_code == 200:
            print("✅ SQL generation working!")
            result = response.json()
            print(f"Generated SQL: {result['generated_sql']}")
        else:
            print(f"❌ SQL generation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ SQL generation error: {e}")

# Test sentiment analysis
def test_sentiment_analysis():
    try:
        payload = {
            "text": "I love this reporting tool! It's amazing and very helpful."
        }
        response = requests.post(
            f"http://localhost:{config.FASTAPI_PORT}/sentiment",
            json=payload
        )
        if response.status_code == 200:
            print("✅ Sentiment analysis working!")
            result = response.json()
            print(f"Sentiment: {result['sentiment']} (confidence: {result['confidence']:.2f})")
        else:
            print(f"❌ Sentiment analysis failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Sentiment analysis error: {e}")

# Run all tests
print("🧪 Testing API endpoints...\n")
test_health_endpoint()
print()
test_schema_retrieval()
print()
test_sql_generation()
print()
test_sentiment_analysis()
print("\n🎉 API testing complete!")