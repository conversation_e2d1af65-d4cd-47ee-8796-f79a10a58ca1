# 🔧 Google Colab Troubleshooting Guide

## 🚨 Common Installation Issues & Solutions

### 1. **Package Version Conflicts**

**Problem:** `ERROR: pip's dependency resolver does not currently consider all the packages that are installed`

**Solution:**
```python
# Run this in a cell BEFORE the installation cell
!pip install --upgrade pip setuptools wheel
!pip cache purge

# Then restart runtime: Runtime > Restart runtime
# Then run the installation cell
```

### 2. **CUDA/GPU Issues**

**Problem:** `RuntimeError: CUDA out of memory` or `No CUDA-capable device is detected`

**Solutions:**
```python
# Check GPU allocation
!nvidia-smi

# Clear GPU memory
import torch
torch.cuda.empty_cache()

# Enable GPU: Runtime > Change runtime type > Hardware accelerator > GPU
```

**If still having issues:**
- Try T4 GPU first (more stable)
- Upgrade to Colab Pro for A100 access
- Reduce model sizes in config

### 3. **Transformers/Hugging Face Issues**

**Problem:** `OSError: Can't load tokenizer` or model download failures

**Solutions:**
```python
# Clear Hugging Face cache
!rm -rf ~/.cache/huggingface/

# Set offline mode if needed
import os
os.environ["TRANSFORMERS_OFFLINE"] = "1"

# Use alternative model hub
os.environ["HUGGINGFACE_HUB_CACHE"] = "/tmp/hf_cache"
```

### 4. **Memory Issues**

**Problem:** `Killed` or `Out of Memory` errors

**Solutions:**
```python
# Check memory usage
!free -h
!df -h

# Clear variables and cache
import gc
gc.collect()

# Use smaller models
WHISPER_MODEL = "tiny"  # instead of "base"
ARCTIC_TEXT2SQL_MODEL = "microsoft/DialoGPT-small"  # fallback
```

### 5. **Network/Download Issues**

**Problem:** `Connection timeout` or `SSL certificate verify failed`

**Solutions:**
```python
# Set longer timeout
import os
os.environ["HF_HUB_DOWNLOAD_TIMEOUT"] = "300"

# Use alternative download method
!pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org [package]

# Check connectivity
!ping -c 3 huggingface.co
```

### 6. **Arctic Model Specific Issues**

**Problem:** `Arctic-Text2SQL-R1-7B` fails to load

**Solutions:**
```python
# Use authentication token (if model requires it)
from huggingface_hub import login
login(token="your_hf_token")

# Fallback to smaller model
ARCTIC_TEXT2SQL_MODEL = "microsoft/DialoGPT-medium"

# Use CPU-only mode
bnb_config = None  # Disable quantization
device_map = "cpu"
```

### 7. **Bitsandbytes Issues**

**Problem:** `bitsandbytes` installation fails

**Solutions:**
```python
# Install specific version
!pip install bitsandbytes==0.41.1 --force-reinstall

# Or skip quantization entirely
USE_QUANTIZATION = False
```

### 8. **FastAPI/Ngrok Issues**

**Problem:** `ngrok` tunnel fails or FastAPI won't start

**Solutions:**
```python
# Kill existing processes
!pkill -f ngrok
!pkill -f uvicorn

# Use alternative port
FASTAPI_PORT = 8080  # instead of 8000

# Get ngrok auth token from https://ngrok.com
!ngrok authtoken YOUR_TOKEN_HERE
```

## 🔄 Step-by-Step Recovery Process

If you encounter multiple issues, follow this recovery process:

### Step 1: Clean Restart
```python
# 1. Runtime > Restart runtime
# 2. Clear all outputs: Edit > Clear all outputs
# 3. Run environment check cell first
```

### Step 2: Minimal Installation
```python
# Install only essential packages first
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
!pip install transformers
!pip install fastapi uvicorn
!pip install pandas numpy
```

### Step 3: Test Basic Functionality
```python
# Test imports
import torch
import transformers
import fastapi
print("✅ Basic imports working")

# Test GPU
print(f"CUDA available: {torch.cuda.is_available()}")
```

### Step 4: Add Components Gradually
```python
# Add one component at a time
!pip install sentence-transformers
!pip install openai-whisper
# Test after each addition
```

## 🆘 Emergency Fallback Configuration

If nothing works, use this minimal configuration:

```python
class MinimalConfig:
    # Use smallest models
    WHISPER_MODEL = "tiny"
    SENTENCE_TRANSFORMER_MODEL = "all-MiniLM-L6-v2"
    ARCTIC_TEXT2SQL_MODEL = "microsoft/DialoGPT-small"  # Fallback
    SENTIMENT_MODEL = "cardiffnlp/twitter-roberta-base-sentiment-latest"
    SUMMARY_MODEL = "t5-small"
    
    # Disable quantization
    USE_QUANTIZATION = False
    
    # Reduce limits
    MAX_RESULTS_ROWS = 100
    ARCTIC_MAX_LENGTH = 512
```

## 📞 Getting Help

### Check These First:
1. **Colab Status**: https://status.cloud.google.com/
2. **GPU Quota**: Check if you've exceeded daily GPU limits
3. **Runtime Type**: Ensure GPU is selected
4. **Python Version**: Should be 3.10+

### Diagnostic Commands:
```python
# System info
!cat /etc/os-release
!python --version
!pip --version

# GPU info
!nvidia-smi
!nvcc --version

# Memory info
!free -h
!df -h

# Network info
!curl -I https://huggingface.co
```

### Common Error Messages & Solutions:

| Error | Solution |
|-------|----------|
| `CUDA out of memory` | Restart runtime, use smaller models |
| `No module named 'X'` | Re-run installation cell |
| `Connection timeout` | Check internet, try again later |
| `Permission denied` | Use `!sudo` prefix |
| `Disk space full` | Clear cache: `!pip cache purge` |

## 💡 Pro Tips

1. **Save Progress**: Download notebook frequently
2. **Use Colab Pro**: More stable, better GPUs
3. **Monitor Resources**: Keep an eye on RAM/GPU usage
4. **Gradual Loading**: Load models one at a time
5. **Cache Models**: Save to Google Drive for reuse

## 🔧 Alternative Approaches

If Colab continues to have issues:

1. **Local Development**: Use your own GPU
2. **Kaggle Notebooks**: Alternative to Colab
3. **Paperspace Gradient**: Another cloud option
4. **AWS SageMaker**: Enterprise solution

---

**Still having issues?** 
- Check the GitHub issues page
- Join the community Discord
- Contact support with your error logs
