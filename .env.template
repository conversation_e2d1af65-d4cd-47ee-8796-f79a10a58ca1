# =============================================================================
# INTELLIGENT REPORTING - PRODUCTION CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env files to version control!

# =============================================================================
# SNOWFLAKE DATABASE CONFIGURATION
# =============================================================================
# Your Snowflake account identifier (format: account.region)
SNOWFLAKE_ACCOUNT='VGXHMTD-ZQ31091.snowflakecomputing.com'

# Snowflake authentication
SNOWFLAKE_USER='readonly_user'
SNOWFLAKE_PASSWORD='readonly_user_123'
SNOWFLAKE_ROLE='readonly_role'

# Snowflake warehouse and database settings
SNOWFLAKE_WAREHOUSE='ARCTIC_QWEN'
SNOWFLAKE_DATABASE='my_reporting_db'
SNOWFLAKE_SCHEMA='BI_LAYER'

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# API key for securing endpoints (generate a strong random key)
API_KEY=your_secure_api_key_here_min_32_chars

# Environment setting (development, staging, production)
ENVIRONMENT=production

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Server port (default: 8000)
PORT=8000

# Debug mode (set to false for production)
DEBUG=false

# Number of worker processes (adjust based on your server capacity)
WORKERS=4

# =============================================================================
# NGROK CONFIGURATION (Development Only)
# =============================================================================
# ngrok auth token (get from https://ngrok.com)
NGROK_AUTH_TOKEN=your_ngrok_auth_token

# Enable ngrok tunnel (set to false for production)
ENABLE_NGROK=false

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================
# Model cache directory (where models are stored locally)
MODEL_CACHE_DIR=./model_cache

# Training data path for LoRA fine-tuning
TRAINING_DATA_PATH=./training_data.json

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Enable metrics collection
ENABLE_METRICS=true

# Metrics server port
METRICS_PORT=9090

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Maximum concurrent requests
MAX_CONCURRENT_REQUESTS=100

# Request timeout in seconds
REQUEST_TIMEOUT=300

# Maximum audio file size in MB
MAX_AUDIO_SIZE_MB=25

# Maximum prompt length
MAX_PROMPT_LENGTH=2000

# Maximum result rows returned
MAX_RESULTS_ROWS=500

# =============================================================================
# ARCTIC MODEL CONFIGURATION
# =============================================================================
# Arctic model generation parameters
ARCTIC_MAX_LENGTH=2048
ARCTIC_TEMPERATURE=0.1
ARCTIC_TOP_P=0.9

# LoRA fine-tuning parameters
LORA_R=16
LORA_ALPHA=32
LORA_DROPOUT=0.1

# =============================================================================
# DATABASE SECURITY
# =============================================================================
# Comma-separated list of allowed schemas
ALLOWED_SCHEMAS=PUBLIC,ANALYTICS,REPORTING

# Comma-separated list of allowed tables (optional, leave empty for schema-level control)
ALLOWED_TABLES=

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins for CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://your-frontend-domain.com

# =============================================================================
# AUDIT & COMPLIANCE
# =============================================================================
# Enable audit logging
ENABLE_AUDIT_LOGGING=true

# Audit log retention in days
LOG_RETENTION_DAYS=30

# Audit log file path
AUDIT_LOG_PATH=./logs/audit.log

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Docker image tag
DOCKER_IMAGE_TAG=latest

# Health check endpoint path
HEALTH_CHECK_PATH=/health

# Graceful shutdown timeout
SHUTDOWN_TIMEOUT=30

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Redis URL for caching (optional)
REDIS_URL=redis://localhost:6379

# Elasticsearch URL for logging (optional)
ELASTICSEARCH_URL=http://localhost:9200

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
# Model backup location
MODEL_BACKUP_PATH=./backups/models

# Configuration backup location
CONFIG_BACKUP_PATH=./backups/config

# Automatic backup interval in hours
BACKUP_INTERVAL_HOURS=24

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Hot reload for development
HOT_RELOAD=false

# Profiling enabled
ENABLE_PROFILING=false

# Mock data for testing
USE_MOCK_DATA=false

# =============================================================================
# NOTES
# =============================================================================
# 1. Replace all placeholder values with your actual configuration
# 2. Use strong, unique passwords and API keys
# 3. Keep this file secure and never commit to version control
# 4. Regularly rotate credentials and API keys
# 5. Monitor logs for any security issues
# 6. Test configuration in staging before production deployment
