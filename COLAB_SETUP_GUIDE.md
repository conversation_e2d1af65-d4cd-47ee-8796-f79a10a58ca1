# 🚀 Intelligent Reporting - Colab Setup Guide

## 📋 Prerequisites

1. **Google Colab Pro** (recommended for T4/A100 GPU access)
2. **Snowflake Account** (for database connections)
3. **ngrok Account** (free tier works fine)

## 🔧 Step-by-Step Setup

### 1. Open the Colab Notebook
- Upload `intelligent_reporting_colab.ipynb` to Google Colab
- Ensure GPU runtime is selected: `Runtime > Change runtime type > GPU`

### 2. Configure Your Credentials (Production-Ready)

**Option A: Environment Variables (Recommended for Production)**

Create a `.env` file in your project directory:

```bash
# Snowflake Configuration
SNOWFLAKE_ACCOUNT=your_account.region
SNOWFLAKE_USER=your_username
SNOWFLAKE_PASSWORD=your_password
SNOWFLAKE_WAREHOUSE=COMPUTE_WH
SNOWFLAKE_DATABASE=your_database
SNOWFLAKE_SCHEMA=your_schema
SNOWFLAKE_ROLE=your_role

# Security
API_KEY=your_secure_api_key_here

# Server Configuration
PORT=8000
DEBUG=false
ENVIRONMENT=production

# ngrok (for development)
NGROK_AUTH_TOKEN=your_ngrok_token
ENABLE_NGROK=false
```

**Option B: Direct Configuration (Development Only)**

For Colab development, you can set environment variables in a cell:

```python
import os
os.environ["SNOWFLAKE_ACCOUNT"] = "your_account.region"
os.environ["SNOWFLAKE_USER"] = "your_username"
os.environ["SNOWFLAKE_PASSWORD"] = "your_password"
os.environ["SNOWFLAKE_WAREHOUSE"] = "COMPUTE_WH"
os.environ["SNOWFLAKE_DATABASE"] = "your_database"
os.environ["SNOWFLAKE_SCHEMA"] = "your_schema"
```

### 3. Run the Notebook Cells
Execute cells in order:

1. **Step 0**: Environment Check (30 seconds) - **RUN THIS FIRST!**
2. **Step 1**: Install Dependencies (3-5 minutes)
3. **Step 2**: Import Libraries
4. **Step 3**: Configuration Setup
5. **Step 4**: Load AI Models (5-10 minutes)
6. **Step 4.5**: LoRA Fine-tuning (Optional)
7. **Step 5**: Data Models & Utilities
8. **Step 6**: FastAPI Endpoints
9. **Step 7**: Start Server & ngrok
10. **Step 8**: Test Endpoints

⚠️ **If you encounter any errors**, check `COLAB_TROUBLESHOOTING.md` for solutions!

### 4. Get Your Public URL
After Step 7, you'll see output like:
```
🎉 Server is running!
📍 Local URL: http://localhost:8000
🌍 Public URL: https://abc123.ngrok.io
📚 API Docs: https://abc123.ngrok.io/docs
```

**Copy the Public URL** - you'll need it for the React frontend!

## 🔍 API Endpoints Overview

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Server health check |
| `/transcribe` | POST | Speech-to-text conversion |
| `/retrieve_schema` | POST | Get relevant database schema |
| `/generate_sql` | POST | Generate SQL from natural language |
| `/execute_sql` | POST | Execute SQL and return results |
| `/sentiment` | POST | Analyze text sentiment |
| `/summarize` | POST | Generate content summary |
| `/download_table` | GET | Download data as CSV/Excel |
| `/download_charts` | GET | Download charts as PNG/SVG |

## 🧪 Testing Your Setup

The notebook includes automated tests in Step 8. You should see:
```
✅ Health endpoint working!
✅ Schema retrieval working!
✅ SQL generation working!
✅ Sentiment analysis working!
```

## 🔧 Troubleshooting

### GPU Memory Issues
If you encounter CUDA out of memory errors:
1. Restart runtime: `Runtime > Restart runtime`
2. Use smaller models in config:
   ```python
   WHISPER_MODEL = "tiny"  # Instead of "base"
   ```

### Model Loading Failures
If Arctic model fails to load:
- The notebook includes automatic fallback to smaller models
- Check the console output for fallback confirmations

### ngrok Connection Issues
If ngrok fails:
1. Sign up for free ngrok account at https://ngrok.com
2. Get your auth token from dashboard
3. Run in a cell: `!ngrok authtoken YOUR_TOKEN`

### Snowflake Connection Issues
- Verify your credentials are correct
- Ensure your IP is whitelisted in Snowflake
- Check network policies in your Snowflake account

## 📊 Performance Expectations

**Model Loading Times (T4 GPU):**
- Whisper: ~30 seconds
- Sentence Transformer: ~15 seconds
- **Arctic-Text2SQL-R1-7B (4-bit)**: ~3-5 minutes
- DistilBERT: ~20 seconds
- T5-Small: ~30 seconds

**Inference Times:**
- Speech-to-text: ~2-5 seconds per minute of audio
- Schema retrieval: ~100-200ms
- **SQL generation (Arctic)**: ~1-3 seconds
- Sentiment analysis: ~50-100ms
- Summarization: ~500ms-1s

## 🔧 LoRA Fine-tuning for Your Schema

The notebook includes **Step 4.5** for fine-tuning the Arctic model on your specific Snowflake schema:

### 1. Create Training Data
```python
# Run this in the notebook to create a template
create_training_data_template()
```

### 2. Edit Training Examples
Edit `/tmp/training_data.json` with your schema-specific examples:

```json
[
  {
    "instruction": "Generate SQL query for the given question and schema.",
    "input": "Question: What are our top customers by revenue?\nSchema: customers(id, name, email), orders(customer_id, amount, date)",
    "output": "SELECT c.name, SUM(o.amount) as total_revenue FROM customers c JOIN orders o ON c.id = o.customer_id GROUP BY c.name ORDER BY total_revenue DESC LIMIT 10;"
  }
]
```

### 3. Run Fine-tuning
```python
# Run this in the notebook after preparing training data
fine_tune_arctic_with_lora()
```

### 4. Benefits of LoRA Fine-tuning
- **Schema-aware**: Better understanding of your specific tables/columns
- **Domain-specific**: Learns your business terminology
- **Improved accuracy**: Higher quality SQL generation
- **Efficient**: Only trains a small subset of parameters

## 🔒 Security Features

The notebook includes:
- ✅ Input sanitization
- ✅ SQL injection prevention
- ✅ Query validation with allow-lists
- ✅ Audit logging
- ✅ Private model inference

## 🎯 Next Steps

Once your Colab backend is running:
1. Copy the ngrok Public URL
2. Proceed to **Step 3: React Frontend Setup**
3. Update the frontend API base URL with your ngrok URL

## 💡 Tips for Production

1. **Model Optimization**: Consider using model quantization for faster inference
2. **Caching**: Implement Redis for schema embeddings
3. **Load Balancing**: Use multiple Colab instances behind a load balancer
4. **Monitoring**: Set up logging and metrics collection
5. **Security**: Implement proper authentication and rate limiting

## 🆘 Getting Help

If you encounter issues:
1. Check the Colab console for error messages
2. Verify all dependencies installed correctly
3. Ensure GPU runtime is selected
4. Check your Snowflake credentials
5. Test individual endpoints using the `/docs` interface

---

**Ready to proceed?** Once your Colab backend is running and tested, we'll move on to creating the React frontend! 🚀
